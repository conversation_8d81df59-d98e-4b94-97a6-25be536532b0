import { BrowserRouter as Router, Routes, Route } from "react-router";
import SignIn from "./pages/AuthPages/SignIn";
import SignUp from "./pages/AuthPages/SignUp";
import VerifyOtp from "./pages/AuthPages/VerifyOtp";
import ResetPassword from "./pages/AuthPages/ResetPassword";
import ProviderSetup from "./pages/Setup/ProviderSetup";
import ServicesManagement from "./pages/Services/ServicesManagement";
import LocationsManagement from "./pages/Locations/LocationsManagement";
import AppointmentsManagement from "./pages/Appointments/AppointmentsManagement";
import QueueManagement from "./pages/Queues/QueueManagement";
import CustomersManagement from "./pages/Customers/CustomersManagement";
import AdvancedFeatures from "./pages/Advanced/AdvancedFeatures";
import NotFound from "./pages/OtherPage/NotFound";
import { ProtectedRoute, PublicRoute } from "./components/auth/ProtectedRoute";
import UserProfiles from "./pages/UserProfiles";
import Videos from "./pages/UiElements/Videos";
import Images from "./pages/UiElements/Images";
import Alerts from "./pages/UiElements/Alerts";
import Badges from "./pages/UiElements/Badges";
import Avatars from "./pages/UiElements/Avatars";
import Buttons from "./pages/UiElements/Buttons";
import LineChart from "./pages/Charts/LineChart";
import BarChart from "./pages/Charts/BarChart";
import Calendar from "./pages/Calendar";
import BasicTables from "./pages/Tables/BasicTables";
import FormElements from "./pages/Forms/FormElements";
import Blank from "./pages/Blank";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import Home from "./pages/Dashboard/Home";
import StandaloneProfileCompletionDemo from "./pages/StandaloneProfileCompletionDemo";
import SearchCommandPalette from "./components/search/SearchCommandPalette";
import ServiceSession from "./pages/ServiceSession/ServiceSession";
import {
  SubscriptionManagementPage,
  BillingHistoryPage,
  SubscriptionPlansPage,
  SubscriptionSuccess,
  SubscriptionCancel
} from "./pages/Subscription";

export default function App() {
  return (
    <>
      <Router>
        <ScrollToTop />
        <SearchCommandPalette />
        <Routes>
          {/* Protected Dashboard Layout */}
          <Route element={
            <ProtectedRoute requireSetupComplete>
              <AppLayout />
            </ProtectedRoute>
          }>
            <Route index path="/" element={<Home />} />

            {/* Provider Pages */}
            <Route path="/profile" element={<UserProfiles />} />
            <Route path="/services" element={<ServicesManagement />} />
            <Route path="/locations" element={<LocationsManagement />} />
            <Route path="/appointments" element={<AppointmentsManagement />} />
            <Route path="/queues" element={<QueueManagement />} />
            <Route path="/customers" element={<CustomersManagement />} />
            <Route path="/advanced" element={<AdvancedFeatures />} />
            <Route path="/calendar" element={<Calendar />} />
            <Route path="/service-session/:appointmentId" element={<ServiceSession />} />
            <Route path="/blank" element={<Blank />} />

            {/* Subscription Pages */}
            <Route path="/subscription" element={<SubscriptionManagementPage />} />
            <Route path="/subscription/billing" element={<BillingHistoryPage />} />
            <Route path="/subscription/plans" element={<SubscriptionPlansPage />} />

            {/* Forms */}
            <Route path="/form-elements" element={<FormElements />} />

            {/* Tables */}
            <Route path="/basic-tables" element={<BasicTables />} />

            {/* Ui Elements */}
            <Route path="/alerts" element={<Alerts />} />
            <Route path="/avatars" element={<Avatars />} />
            <Route path="/badge" element={<Badges />} />
            <Route path="/buttons" element={<Buttons />} />
            <Route path="/images" element={<Images />} />
            <Route path="/videos" element={<Videos />} />

            {/* Charts */}
            <Route path="/line-chart" element={<LineChart />} />
            <Route path="/bar-chart" element={<BarChart />} />
          </Route>

          {/* Setup Route (Protected but doesn't require setup completion) */}
          <Route path="/setup" element={
            <ProtectedRoute requireSetupComplete={false}>
              <ProviderSetup />
            </ProtectedRoute>
          } />

          {/* Subscription Callback Routes (Protected but doesn't require setup completion) */}
          <Route path="/subscription/success" element={
            <ProtectedRoute requireSetupComplete={false}>
              <SubscriptionSuccess />
            </ProtectedRoute>
          } />
          <Route path="/subscription/cancel" element={
            <ProtectedRoute requireSetupComplete={false}>
              <SubscriptionCancel />
            </ProtectedRoute>
          } />



          {/* Public Auth Routes */}
          <Route path="/signin" element={
            <PublicRoute>
              <SignIn />
            </PublicRoute>
          } />
          <Route path="/signup" element={
            <PublicRoute>
              <SignUp />
            </PublicRoute>
          } />
          <Route path="/verify-otp" element={
            <PublicRoute>
              <VerifyOtp />
            </PublicRoute>
          } />
          <Route path="/reset-password" element={
            <PublicRoute>
              <ResetPassword />
            </PublicRoute>
          } />

          {/* Demo Routes (Public Access) */}
          <Route path="/profile-completion-demo" element={<StandaloneProfileCompletionDemo />} />

          {/* Fallback Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </>
  );
}
