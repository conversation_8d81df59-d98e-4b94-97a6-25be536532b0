import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  SubscriptionStatusResponse,
  SubscriptionPlansApiResponse,
  CheckoutRequest,
  CheckoutResponse,
  SubscriptionCancellationRequest,
  BillingHistoryApiResponse,
  UserCreditsApiResponse,
  WebhookConfig,
  SubscriptionUpgradeRequest,
  SubscriptionDowngradeRequest,
} from '../types';

/**
 * Subscription service for managing provider subscriptions, billing, and credits
 */
export class SubscriptionService {
  /**
   * Get current subscription status for the authenticated provider
   */
  static async getSubscriptionStatus(): Promise<SubscriptionStatusResponse> {
    const response = await apiClient.get<SubscriptionStatusResponse>(
      config.endpoints.subscriptions.overview
    );
    return response.data;
  }

  /**
   * Get all available subscription plans (for authenticated users)
   */
  static async getAvailablePlans(): Promise<SubscriptionPlansApiResponse> {
    const response = await apiClient.get<SubscriptionPlansApiResponse>(
      config.endpoints.subscriptions.plans
    );
    return response.data;
  }

  /**
   * Get available subscription plans for external sites
   */
  static async getExternalSubscriptionPlans(): Promise<SubscriptionPlansApiResponse> {
    const response = await apiClient.get<SubscriptionPlansApiResponse>(
      config.endpoints.subscriptions.externalPlans
    );
    return response.data;
  }

  /**
   * Subscribe to a plan (direct subscription)
   */
  static async subscribeToPlan(planId: string): Promise<any> {
    const response = await apiClient.post(
      config.endpoints.subscriptions.subscribe,
      { planId }
    );
    return response.data;
  }

  /**
   * Create external checkout session
   */
  static async createCheckoutSession(data: CheckoutRequest): Promise<CheckoutResponse> {
    const response = await apiClient.post<CheckoutResponse>(
      config.endpoints.subscriptions.externalCheckout,
      data
    );
    return response.data;
  }

  /**
   * Create subscription checkout session
   */
  static async createSubscriptionCheckout(data: CheckoutRequest): Promise<CheckoutResponse> {
    const response = await apiClient.post<CheckoutResponse>(
      config.endpoints.subscriptions.subscriptionCheckout,
      data
    );
    return response.data;
  }

  /**
   * Get checkout session status
   */
  static async getCheckoutSession(sessionId: string): Promise<any> {
    const response = await apiClient.get(
      `${config.endpoints.subscriptions.checkoutSession}/${sessionId}`
    );
    return response.data;
  }

  /**
   * Update user subscription
   */
  static async updateSubscription(data: any): Promise<any> {
    const response = await apiClient.put(
      config.endpoints.subscriptions.updateSubscription,
      data
    );
    return response.data;
  }

  /**
   * Get credit history
   */
  static async getCreditHistory(): Promise<any> {
    const response = await apiClient.get(
      config.endpoints.subscriptions.creditsHistory
    );
    return response.data;
  }

  /**
   * Get user credits information
   */
  static async getUserCredits(): Promise<UserCreditsApiResponse> {
    const response = await apiClient.get<UserCreditsApiResponse>(
      config.endpoints.subscriptions.credits
    );
    return response.data;
  }

  /**
   * Update user credits
   */
  static async updateUserCredits(data: any): Promise<any> {
    const response = await apiClient.post(
      config.endpoints.subscriptions.credits,
      data
    );
    return response.data;
  }

  /**
   * Upgrade subscription to a higher plan
   */
  static async upgradeSubscription(data: SubscriptionUpgradeRequest): Promise<CheckoutResponse> {
    // For upgrades, we create a new checkout session with the new plan
    const checkoutData: CheckoutRequest = {
      planIdentifier: data.newPlanId,
      returnUrl: `${window.location.origin}/subscription/success?upgrade=true`,
      cancelUrl: `${window.location.origin}/subscription/cancel`,
      customerInfo: {
        email: '', // Will be populated from auth context
        name: '', // Will be populated from auth context
      },
    };

    return this.createCheckoutSession(checkoutData);
  }

  /**
   * Downgrade subscription to a lower plan
   */
  static async downgradeSubscription(data: SubscriptionDowngradeRequest): Promise<CheckoutResponse> {
    // For downgrades, we create a new checkout session with the new plan
    const checkoutData: CheckoutRequest = {
      planIdentifier: data.newPlanId,
      returnUrl: `${window.location.origin}/subscription/success?downgrade=true`,
      cancelUrl: `${window.location.origin}/subscription/cancel`,
      customerInfo: {
        email: '', // Will be populated from auth context
        name: '', // Will be populated from auth context
      },
    };

    return this.createCheckoutSession(checkoutData);
  }

  /**
   * Register webhook endpoint for subscription events (optional)
   */
  static async registerWebhook(config: WebhookConfig): Promise<{ message: string }> {
    const response = await apiClient.post<{ message: string }>(
      config.endpoints.subscriptions.webhooks,
      config
    );
    return response.data;
  }

  /**
   * Check if user has sufficient credits for an operation
   */
  static async checkCreditsAvailability(userId: string, requiredCredits: number): Promise<boolean> {
    try {
      const creditsResponse = await this.getUserCredits(userId);
      if (creditsResponse.success) {
        return creditsResponse.data.credits >= requiredCredits;
      }
      return false;
    } catch (error) {
      console.error('Failed to check credits availability:', error);
      return false;
    }
  }

  /**
   * Get subscription status with error handling
   */
  static async getSubscriptionStatusSafe(): Promise<SubscriptionStatusResponse | null> {
    try {
      return await this.getSubscriptionStatus();
    } catch (error) {
      console.error('Failed to fetch subscription status:', error);
      return null;
    }
  }

  /**
   * Create checkout session with provider context
   */
  static async createCheckoutWithContext(
    planId: string,
    providerEmail: string,
    providerName: string,
    returnUrl?: string,
    cancelUrl?: string
  ): Promise<CheckoutResponse> {
    const checkoutData: CheckoutRequest = {
      planIdentifier: planId,
      returnUrl: returnUrl || `${window.location.origin}/subscription/success`,
      cancelUrl: cancelUrl || `${window.location.origin}/subscription/cancel`,
      customerInfo: {
        email: providerEmail,
        name: providerName,
      },
    };

    return this.createCheckoutSession(checkoutData);
  }

  /**
   * Validate subscription status for feature access
   */
  static async validateFeatureAccess(feature: string): Promise<boolean> {
    try {
      const statusResponse = await this.getSubscriptionStatus();
      if (!statusResponse.success || !statusResponse.data.hasActiveSubscription) {
        return false;
      }

      // Add feature-specific validation logic here
      // For now, return true if user has active subscription
      return statusResponse.data.subscription?.status === 'active';
    } catch (error) {
      console.error('Failed to validate feature access:', error);
      return false;
    }
  }

  /**
   * Get credit usage analytics
   */
  static async getCreditUsageAnalytics(userId: string, days: number = 30): Promise<any> {
    try {
      const creditsResponse = await this.getUserCredits(userId);
      if (!creditsResponse.success) {
        return null;
      }

      const history = creditsResponse.data.creditHistory;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      // Filter history to the specified period
      const recentHistory = history.filter(entry => 
        new Date(entry.date) >= cutoffDate
      );

      // Calculate analytics
      const totalUsed = recentHistory
        .filter(entry => entry.change < 0)
        .reduce((sum, entry) => sum + Math.abs(entry.change), 0);

      const averageDaily = totalUsed / days;

      return {
        totalUsed,
        averageDaily,
        recentHistory,
        currentBalance: creditsResponse.data.credits,
      };
    } catch (error) {
      console.error('Failed to get credit usage analytics:', error);
      return null;
    }
  }

  /**
   * Check if subscription is expiring soon
   */
  static async isSubscriptionExpiringSoon(daysThreshold: number = 7): Promise<boolean> {
    try {
      const statusResponse = await this.getSubscriptionStatus();
      if (!statusResponse.success || !statusResponse.data.hasActiveSubscription) {
        return false;
      }

      const subscription = statusResponse.data.subscription;
      if (!subscription) return false;

      return subscription.daysRemaining <= daysThreshold;
    } catch (error) {
      console.error('Failed to check subscription expiration:', error);
      return false;
    }
  }

  /**
   * Check if credits are running low
   */
  static async areCreditsLow(userId: string, threshold: number = 0.2): Promise<boolean> {
    try {
      const statusResponse = await this.getSubscriptionStatus();
      if (!statusResponse.success) {
        return false;
      }

      const credits = statusResponse.data.credits;
      if (!credits) return false;

      const usagePercentage = credits.percentage / 100;
      return usagePercentage <= threshold;
    } catch (error) {
      console.error('Failed to check credit levels:', error);
      return false;
    }
  }
}
