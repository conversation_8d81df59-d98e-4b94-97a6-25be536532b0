import React from 'react';
import { useNavigate } from 'react-router';
import { SubscriptionPlans } from '../../components/subscription';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';

const SubscriptionPlansPage: React.FC = () => {
  const navigate = useNavigate();
  const { provider } = useAuth();
  const { data: subscriptionResponse } = useSubscriptionStatus();

  const currentPlanId = subscriptionResponse?.data?.subscription?.id;

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <button
            onClick={() => navigate('/subscription')}
            className="mr-4 inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back
          </button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Subscription Plans
          </h1>
        </div>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Choose the perfect plan for {provider?.title || 'your business'}. 
          All plans include secure payment processing, customer management, and 24/7 support.
        </p>
      </div>

      {/* Current Plan Info */}
      {subscriptionResponse?.data?.hasActiveSubscription && (
        <div className="max-w-4xl mx-auto">
          <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800/50 rounded-lg p-6">
            <div className="flex items-center">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Current Plan: {subscriptionResponse.data.subscription?.name}
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  You can upgrade or downgrade your plan at any time. Changes will be prorated.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Plans Component */}
      <SubscriptionPlans 
        currentPlanId={currentPlanId}
        showCurrentPlan={true}
        highlightUpgrades={true}
      />

      {/* FAQ Section */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-8">
          Frequently Asked Questions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                Can I change my plan anytime?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Yes, you can upgrade or downgrade your plan at any time. 
                Upgrades take effect immediately, while downgrades take effect at the next billing cycle.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                What happens to unused credits?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Unused credits roll over to the next billing period for active subscriptions. 
                Credits expire 12 months after allocation.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                Is there a setup fee?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                No, there are no setup fees or hidden charges. 
                You only pay the monthly or yearly subscription fee.
              </p>
            </div>
          </div>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                Can I cancel anytime?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Yes, you can cancel your subscription at any time. 
                You'll continue to have access until the end of your current billing period.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                Do you offer refunds?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                We offer a 30-day money-back guarantee for new subscriptions. 
                Contact support for refund requests.
              </p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                We accept all major credit cards, PayPal, and bank transfers. 
                All payments are processed securely.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Need Help Choosing a Plan?
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Our team is here to help you find the perfect plan for your business needs.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <button
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              Contact Sales
            </button>
            <button
              onClick={() => window.open('https://calendly.com/dalti-demo', '_blank')}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors dark:border-gray-600 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
            >
              Schedule Demo
            </button>
          </div>
        </div>
      </div>

      {/* Trust Indicators */}
      <div className="max-w-4xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Secure & Reliable</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Bank-level security with 99.9% uptime guarantee
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">24/7 Support</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Expert support team available around the clock
            </p>
          </div>
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-3">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-1">Fast Setup</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Get started in minutes with our easy setup process
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlansPage;
