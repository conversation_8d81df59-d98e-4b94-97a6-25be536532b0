# Provider Dashboard Subscription System Integration Guide

## 🎯 Overview

This guide provides comprehensive instructions for integrating the Dalti Subscription System into your **Provider Dashboard Website**. The subscription system is hosted on the main backend and provides APIs for managing provider subscriptions, billing, and credits.

## 🏗️ Architecture Overview

```
Provider Dashboard Website (Frontend)
           ↓ API Calls
Main Backend (Dalti API)
    ├── Subscription Management
    ├── Payment Processing  
    ├── Credit System
    └── Analytics & Reporting
```

### Key Components
- **Backend API**: Handles all subscription logic and data
- **Provider Dashboard**: Consumes APIs to display subscription UI
- **Authentication**: JWT-based authentication for providers
- **Real-time Updates**: Webhooks for subscription events

## 🔐 Authentication Setup

### Base URLs
- **Production**: `https://api.dalti.app`
- **Staging**: `https://api-staging.dalti.app`
- **Development**: `http://localhost:5400`

### Authentication Methods

#### 1. JWT Token Authentication (Recommended for Provider Dashboard)
```javascript
// Include JWT token in all API requests
const headers = {
  'Authorization': `Bearer ${providerJwtToken}`,
  'Content-Type': 'application/json'
};
```

#### 2. API Key Authentication (For Server-Side Operations)
```javascript
// For server-side operations
const headers = {
  'X-API-Key': 'your-api-key',
  'Content-Type': 'application/json'
};
```

## 📋 Core API Endpoints for Provider Dashboard

### 1. Provider Subscription Status
```http
GET /api/auth/providers/subscription-status
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "hasActiveSubscription": true,
    "subscription": {
      "id": "sub_123",
      "name": "Pro Plan",
      "status": "active",
      "startDate": "2025-01-01T00:00:00.000Z",
      "endDate": "2025-02-01T00:00:00.000Z",
      "daysRemaining": 15,
      "creditsAllocated": 1000,
      "price": 2000,
      "priceFormatted": "$20.00",
      "interval": "monthly"
    },
    "credits": {
      "current": 750,
      "allocated": 1000,
      "used": 250,
      "percentage": 75
    }
  }
}
```

### 2. Available Subscription Plans
```http
GET /api/external/subscriptions
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": "free-plan",
        "name": "Free Plan",
        "description": "Perfect for getting started",
        "price": 0,
        "priceFormatted": "$0.00",
        "interval": "monthly",
        "creditsIncluded": 100,
        "features": [
          "Basic appointment management",
          "Up to 100 credits/month",
          "Email support"
        ],
        "isActive": true,
        "isPopular": false
      },
      {
        "id": "hobby-plan", 
        "name": "Hobby Plan",
        "description": "Great for small practices",
        "price": 500,
        "priceFormatted": "$5.00",
        "interval": "monthly",
        "creditsIncluded": 500,
        "features": [
          "Advanced appointment management",
          "Up to 500 credits/month",
          "Priority email support",
          "Basic analytics"
        ],
        "isActive": true,
        "isPopular": true
      },
      {
        "id": "pro-plan",
        "name": "Pro Plan", 
        "description": "Perfect for growing businesses",
        "price": 2000,
        "priceFormatted": "$20.00",
        "interval": "monthly",
        "creditsIncluded": 2000,
        "features": [
          "Full appointment management",
          "Up to 2000 credits/month",
          "24/7 priority support",
          "Advanced analytics",
          "Custom integrations"
        ],
        "isActive": true,
        "isPopular": false
      }
    ],
    "total": 3
  }
}
```

### 3. Create Subscription Checkout
```http
POST /api/external/checkout
Authorization: Bearer {jwt-token}
```

**Request Body:**
```json
{
  "planIdentifier": "pro-plan",
  "returnUrl": "https://provider-dashboard.com/subscription/success",
  "cancelUrl": "https://provider-dashboard.com/subscription/cancel",
  "customerInfo": {
    "email": "<EMAIL>",
    "name": "Dr. John Smith"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "cs_123456789",
    "sessionUrl": "https://checkout.dalti.app/session/cs_123456789",
    "expiresAt": "2025-07-22T12:00:00.000Z"
  }
}
```

### 4. Subscription History
```http
GET /api/auth/subscriptions/history
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptions": [
      {
        "id": "sub_123",
        "subscriptionName": "Pro Plan",
        "status": "active",
        "startDate": "2025-01-01T00:00:00.000Z",
        "endDate": "2025-02-01T00:00:00.000Z",
        "price": 2000,
        "priceFormatted": "$20.00",
        "creditsAllocated": 2000
      }
    ],
    "total": 1
  }
}
```

### 5. Cancel Subscription
```http
POST /api/auth/subscriptions/cancel
Authorization: Bearer {jwt-token}
```

**Request Body:**
```json
{
  "subscriptionId": "sub_123",
  "reason": "No longer needed",
  "feedback": "Service was great, but scaling down operations"
}
```

### 6. Provider Credits Information
```http
GET /api/users/credits?userId={provider-user-id}
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "user_123",
    "credits": 750,
    "lastUpdated": "2025-07-22T08:00:00.000Z",
    "creditHistory": [
      {
        "date": "2025-07-22T08:00:00.000Z",
        "change": -50,
        "reason": "Appointment booking",
        "balance": 750
      },
      {
        "date": "2025-07-01T00:00:00.000Z", 
        "change": +2000,
        "reason": "Monthly subscription renewal",
        "balance": 800
      }
    ]
  }
}
```

## 🎨 Provider Dashboard UI Components

### 1. Subscription Status Card

```jsx
import React, { useState, useEffect } from 'react';

const SubscriptionStatusCard = ({ providerToken }) => {
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/auth/providers/subscription-status', {
        headers: {
          'Authorization': `Bearer ${providerToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setSubscription(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch subscription status:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading subscription status...</div>;

  if (!subscription?.hasActiveSubscription) {
    return (
      <div className="subscription-card no-subscription">
        <h3>No Active Subscription</h3>
        <p>Upgrade to a paid plan to access premium features</p>
        <button onClick={() => window.location.href = '/subscription/plans'}>
          View Plans
        </button>
      </div>
    );
  }

  const { subscription: sub, credits } = subscription;

  return (
    <div className="subscription-card active">
      <div className="subscription-header">
        <h3>{sub.name}</h3>
        <span className={`status ${sub.status}`}>{sub.status}</span>
      </div>
      
      <div className="subscription-details">
        <div className="detail-item">
          <label>Price:</label>
          <span>{sub.priceFormatted}/{sub.interval}</span>
        </div>
        
        <div className="detail-item">
          <label>Next Billing:</label>
          <span>{new Date(sub.endDate).toLocaleDateString()}</span>
        </div>
        
        <div className="detail-item">
          <label>Days Remaining:</label>
          <span>{sub.daysRemaining} days</span>
        </div>
      </div>

      <div className="credits-section">
        <h4>Credits Usage</h4>
        <div className="credits-bar">
          <div 
            className="credits-progress" 
            style={{ width: `${credits.percentage}%` }}
          ></div>
        </div>
        <p>{credits.current} / {credits.allocated} credits remaining</p>
      </div>

      <div className="subscription-actions">
        <button onClick={() => window.location.href = '/subscription/manage'}>
          Manage Subscription
        </button>
        <button onClick={() => window.location.href = '/subscription/billing'}>
          View Billing
        </button>
      </div>
    </div>
  );
};

export default SubscriptionStatusCard;
```

### 2. Subscription Plans Component

```jsx
import React, { useState, useEffect } from 'react';

const SubscriptionPlans = ({ providerToken, currentPlan }) => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/external/subscriptions', {
        headers: {
          'Authorization': `Bearer ${providerToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setPlans(data.data.subscriptions);
      }
    } catch (error) {
      console.error('Failed to fetch plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectPlan = async (planId) => {
    try {
      const response = await fetch('/api/external/checkout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${providerToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          planIdentifier: planId,
          returnUrl: window.location.origin + '/subscription/success',
          cancelUrl: window.location.origin + '/subscription/cancel',
          customerInfo: {
            email: '<EMAIL>', // Get from provider context
            name: 'Provider Name' // Get from provider context
          }
        })
      });

      const checkout = await response.json();
      if (checkout.success) {
        window.location.href = checkout.data.sessionUrl;
      }
    } catch (error) {
      console.error('Failed to create checkout:', error);
    }
  };

  if (loading) return <div>Loading subscription plans...</div>;

  return (
    <div className="subscription-plans">
      <h2>Choose Your Subscription Plan</h2>
      
      <div className="plans-grid">
        {plans.map((plan) => (
          <div 
            key={plan.id} 
            className={`plan-card ${plan.isPopular ? 'popular' : ''} ${currentPlan === plan.id ? 'current' : ''}`}
          >
            {plan.isPopular && <div className="popular-badge">Most Popular</div>}
            
            <div className="plan-header">
              <h3>{plan.name}</h3>
              <div className="price">
                <span className="amount">{plan.priceFormatted}</span>
                <span className="interval">/{plan.interval}</span>
              </div>
            </div>

            <p className="plan-description">{plan.description}</p>

            <div className="plan-features">
              <div className="credits-info">
                <strong>{plan.creditsIncluded} credits included</strong>
              </div>
              
              <ul>
                {plan.features.map((feature, index) => (
                  <li key={index}>✅ {feature}</li>
                ))}
              </ul>
            </div>

            <div className="plan-actions">
              {currentPlan === plan.id ? (
                <button className="current-plan-btn" disabled>
                  Current Plan
                </button>
              ) : (
                <button 
                  className="select-plan-btn"
                  onClick={() => selectPlan(plan.id)}
                >
                  {plan.price === 0 ? 'Get Started' : 'Upgrade to ' + plan.name}
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SubscriptionPlans;
```

### 3. Credits Usage Component

```jsx
import React, { useState, useEffect } from 'react';

const CreditsUsage = ({ providerToken, userId }) => {
  const [creditsData, setCreditsData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCreditsData();
  }, []);

  const fetchCreditsData = async () => {
    try {
      const response = await fetch(`/api/users/credits?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${providerToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      if (data.success) {
        setCreditsData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch credits data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading credits information...</div>;

  return (
    <div className="credits-usage">
      <div className="credits-header">
        <h3>Credits Usage</h3>
        <div className="current-credits">
          <span className="credits-amount">{creditsData.credits}</span>
          <span className="credits-label">credits remaining</span>
        </div>
      </div>

      <div className="credits-history">
        <h4>Recent Activity</h4>
        <div className="history-list">
          {creditsData.creditHistory.map((entry, index) => (
            <div key={index} className="history-item">
              <div className="history-date">
                {new Date(entry.date).toLocaleDateString()}
              </div>
              <div className="history-reason">{entry.reason}</div>
              <div className={`history-change ${entry.change > 0 ? 'positive' : 'negative'}`}>
                {entry.change > 0 ? '+' : ''}{entry.change}
              </div>
              <div className="history-balance">
                Balance: {entry.balance}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CreditsUsage;
```

## 🔗 Webhook Integration (Optional)

If you want real-time updates in your provider dashboard, you can set up webhooks:

### 1. Register Webhook Endpoint

```javascript
const registerWebhook = async () => {
  const webhookConfig = {
    url: 'https://provider-dashboard.com/webhooks/subscription',
    secret: 'your-webhook-secret',
    events: [
      'subscription.created',
      'subscription.updated',
      'subscription.cancelled',
      'payment.succeeded',
      'payment.failed',
      'credits.updated'
    ]
  };

  const response = await fetch('/api/webhooks/callbacks', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${providerToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(webhookConfig)
  });
};
```

### 2. Handle Webhook Events (Backend)

```javascript
// Express.js webhook handler
app.post('/webhooks/subscription', express.raw({type: 'application/json'}), (req, res) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = req.body;
  
  // Verify webhook signature
  const expectedSignature = crypto
    .createHmac('sha256', process.env.WEBHOOK_SECRET)
    .update(payload)
    .digest('hex');
    
  if (signature !== expectedSignature) {
    return res.status(401).send('Invalid signature');
  }
  
  const event = JSON.parse(payload);
  
  switch (event.eventType) {
    case 'subscription.created':
      // Update provider dashboard UI
      notifyProviderDashboard('subscription_updated', event.data);
      break;
      
    case 'credits.updated':
      // Update credits display
      notifyProviderDashboard('credits_updated', event.data);
      break;
      
    case 'payment.failed':
      // Show payment failure notification
      notifyProviderDashboard('payment_failed', event.data);
      break;
  }
  
  res.status(200).send('OK');
});
```

## 🎨 CSS Styling Examples

```css
/* Subscription Status Card */
.subscription-card {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subscription-card.active {
  border-color: #28a745;
}

.subscription-card.no-subscription {
  border-color: #ffc107;
  background: #fff3cd;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status.active {
  background: #d4edda;
  color: #155724;
}

.credits-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 8px 0;
}

.credits-progress {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s ease;
}

/* Subscription Plans */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 24px 0;
}

.plan-card {
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 24px;
  position: relative;
  transition: all 0.3s ease;
}

.plan-card:hover {
  border-color: #007bff;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.plan-card.popular {
  border-color: #007bff;
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #007bff;
  color: white;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: bold;
}

.price {
  margin: 16px 0;
}

.price .amount {
  font-size: 2.5em;
  font-weight: bold;
  color: #007bff;
}

.price .interval {
  font-size: 1em;
  color: #6c757d;
}

.plan-features ul {
  list-style: none;
  padding: 0;
}

.plan-features li {
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.select-plan-btn {
  width: 100%;
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
}

.select-plan-btn:hover {
  background: #0056b3;
}

.current-plan-btn {
  width: 100%;
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: not-allowed;
}
```

## ❌ Error Handling

### Standard Error Response Format

All API endpoints return errors in a consistent format:

```json
{
  "success": false,
  "message": "Human-readable error message",
  "error": "Technical error details"
}
```

### Common HTTP Status Codes

| Code | Meaning | Action Required |
|------|---------|----------------|
| 200 | Success | Continue normally |
| 401 | Unauthorized | Refresh JWT token |
| 403 | Forbidden | Check provider permissions |
| 404 | Not Found | Handle missing resource |
| 429 | Rate Limited | Implement retry logic |
| 500 | Server Error | Show error message |

### Error Handling Implementation

```javascript
// API utility with error handling
class SubscriptionAPI {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async makeRequest(endpoint, options = {}) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });

      const data = await response.json();

      if (!response.ok) {
        throw new APIError(data.message || 'Request failed', response.status, data);
      }

      if (!data.success) {
        throw new APIError(data.message || 'API request failed', response.status, data);
      }

      return data.data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError('Network error: ' + error.message, 0, null);
    }
  }

  // Subscription methods
  async getSubscriptionStatus() {
    return this.makeRequest('/api/auth/providers/subscription-status');
  }

  async getAvailablePlans() {
    return this.makeRequest('/api/external/subscriptions');
  }

  async createCheckout(planId, returnUrl, cancelUrl, customerInfo) {
    return this.makeRequest('/api/external/checkout', {
      method: 'POST',
      body: JSON.stringify({
        planIdentifier: planId,
        returnUrl,
        cancelUrl,
        customerInfo
      })
    });
  }

  async cancelSubscription(subscriptionId, reason, feedback) {
    return this.makeRequest('/api/auth/subscriptions/cancel', {
      method: 'POST',
      body: JSON.stringify({
        subscriptionId,
        reason,
        feedback
      })
    });
  }

  async getCredits(userId) {
    return this.makeRequest(`/api/users/credits?userId=${userId}`);
  }
}

class APIError extends Error {
  constructor(message, status, data) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.data = data;
  }
}

// Usage with error handling
const subscriptionAPI = new SubscriptionAPI('https://api.dalti.app', providerToken);

try {
  const subscriptionStatus = await subscriptionAPI.getSubscriptionStatus();
  updateUI(subscriptionStatus);
} catch (error) {
  if (error instanceof APIError) {
    switch (error.status) {
      case 401:
        // Token expired, redirect to login
        redirectToLogin();
        break;
      case 403:
        showError('Access denied. Please contact support.');
        break;
      case 429:
        showError('Too many requests. Please try again later.');
        break;
      default:
        showError('Error: ' + error.message);
    }
  } else {
    showError('Network error. Please check your connection.');
  }
}
```

## 🧪 Testing Your Integration

### 1. Test Environment Setup

```javascript
// Test configuration
const TEST_CONFIG = {
  baseUrl: 'https://api-staging.dalti.app',
  testToken: 'your-test-jwt-token',
  testUserId: 'test-provider-user-id'
};

// Initialize API client for testing
const testAPI = new SubscriptionAPI(TEST_CONFIG.baseUrl, TEST_CONFIG.testToken);
```

### 2. Integration Test Suite

```javascript
// Test subscription status endpoint
async function testSubscriptionStatus() {
  console.log('Testing subscription status...');

  try {
    const status = await testAPI.getSubscriptionStatus();
    console.log('✅ Subscription status:', status);

    // Verify response structure
    assert(typeof status.hasActiveSubscription === 'boolean');
    if (status.hasActiveSubscription) {
      assert(status.subscription.id);
      assert(status.subscription.name);
      assert(status.credits.current >= 0);
    }
  } catch (error) {
    console.error('❌ Subscription status test failed:', error);
  }
}

// Test available plans endpoint
async function testAvailablePlans() {
  console.log('Testing available plans...');

  try {
    const plans = await testAPI.getAvailablePlans();
    console.log('✅ Available plans:', plans);

    // Verify response structure
    assert(Array.isArray(plans.subscriptions));
    assert(plans.subscriptions.length > 0);

    plans.subscriptions.forEach(plan => {
      assert(plan.id);
      assert(plan.name);
      assert(typeof plan.price === 'number');
      assert(Array.isArray(plan.features));
    });
  } catch (error) {
    console.error('❌ Available plans test failed:', error);
  }
}

// Test checkout creation
async function testCheckoutCreation() {
  console.log('Testing checkout creation...');

  try {
    const checkout = await testAPI.createCheckout(
      'hobby-plan',
      'https://provider-dashboard.com/success',
      'https://provider-dashboard.com/cancel',
      {
        email: '<EMAIL>',
        name: 'Test Provider'
      }
    );

    console.log('✅ Checkout created:', checkout);

    // Verify response structure
    assert(checkout.sessionId);
    assert(checkout.sessionUrl);
    assert(checkout.expiresAt);
  } catch (error) {
    console.error('❌ Checkout creation test failed:', error);
  }
}

// Run all tests
async function runIntegrationTests() {
  console.log('🧪 Starting integration tests...');

  await testSubscriptionStatus();
  await testAvailablePlans();
  await testCheckoutCreation();

  console.log('✅ All integration tests completed!');
}

// Helper function
function assert(condition, message = 'Assertion failed') {
  if (!condition) {
    throw new Error(message);
  }
}
```

## 📋 Implementation Checklist

### Phase 1: Basic Integration (Week 1)
- [ ] **Set up API client** with authentication
- [ ] **Implement subscription status** display
- [ ] **Add subscription plans** page
- [ ] **Create checkout flow** integration
- [ ] **Test basic functionality** with staging API

### Phase 2: Enhanced Features (Week 2)
- [ ] **Add credits usage** tracking and display
- [ ] **Implement subscription history** page
- [ ] **Add subscription cancellation** flow
- [ ] **Create billing information** display
- [ ] **Add error handling** and user feedback

### Phase 3: Advanced Features (Week 3)
- [ ] **Set up webhook integration** (optional)
- [ ] **Add real-time updates** for subscription changes
- [ ] **Implement subscription upgrade/downgrade** flow
- [ ] **Add usage analytics** and reporting
- [ ] **Create admin subscription management** (if needed)

### Phase 4: Polish & Production (Week 4)
- [ ] **Add comprehensive error handling**
- [ ] **Implement loading states** and UI feedback
- [ ] **Add responsive design** for mobile devices
- [ ] **Perform thorough testing** with production API
- [ ] **Deploy to production** environment

## 🔧 Configuration & Environment Variables

```javascript
// Environment configuration
const CONFIG = {
  development: {
    apiBaseUrl: 'http://localhost:5400',
    checkoutBaseUrl: 'http://localhost:3000'
  },
  staging: {
    apiBaseUrl: 'https://api-staging.dalti.app',
    checkoutBaseUrl: 'https://checkout-staging.dalti.app'
  },
  production: {
    apiBaseUrl: 'https://api.dalti.app',
    checkoutBaseUrl: 'https://checkout.dalti.app'
  }
};

// Get current environment config
const currentConfig = CONFIG[process.env.NODE_ENV || 'development'];
```

## 📞 Support & Resources

### 🆘 Getting Help
- **Technical Support**: <EMAIL>
- **Integration Questions**: <EMAIL>
- **Response Time**: < 24 hours

### 📖 Additional Resources
- **API Status**: https://status.dalti.app
- **Full API Documentation**: [External Integration Guide](./EXTERNAL_INTEGRATION_GUIDE.md)
- **Quick Reference**: [Quick Reference Guide](./QUICK_REFERENCE.md)

### 🐛 Common Issues & Solutions

#### Issue: JWT Token Expired
```javascript
// Solution: Implement token refresh
if (error.status === 401) {
  const newToken = await refreshProviderToken();
  // Retry the request with new token
}
```

#### Issue: Checkout Session Expired
```javascript
// Solution: Create new checkout session
if (checkoutError.includes('expired')) {
  const newCheckout = await createNewCheckoutSession(planId);
  window.location.href = newCheckout.sessionUrl;
}
```

#### Issue: Credits Not Updating
```javascript
// Solution: Force refresh credits data
const refreshCredits = async () => {
  const credits = await subscriptionAPI.getCredits(userId);
  updateCreditsDisplay(credits);
};
```

## 🎯 Success Metrics

### Key Performance Indicators
- **Subscription Conversion Rate**: > 15%
- **API Response Time**: < 200ms
- **Error Rate**: < 1%
- **User Satisfaction**: > 4.5/5

### Monitoring & Analytics
- Track subscription plan selections
- Monitor checkout completion rates
- Analyze credit usage patterns
- Measure user engagement with subscription features

---

## 🚀 Ready to Integrate?

1. **📖 Review this documentation** thoroughly
2. **🔑 Obtain your JWT tokens** for authentication
3. **🧪 Test with staging environment** first
4. **🎨 Implement UI components** step by step
5. **🚀 Deploy to production** with confidence

**The subscription system is fully functional and ready for integration!** 🎉

**Need help?** Contact our support <NAME_EMAIL>
```
