import React, { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router';
import toast from 'react-hot-toast';
import { useCreditsLowCheck, useSubscriptionStatus } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';

interface CreditMonitorProps {
  lowCreditThreshold?: number; // Percentage threshold for low credits (default: 20%)
  criticalCreditThreshold?: number; // Percentage threshold for critical credits (default: 10%)
  enableToastNotifications?: boolean; // Enable toast notifications (default: true)
  enableBrowserNotifications?: boolean; // Enable browser notifications (default: true)
  checkInterval?: number; // Check interval in milliseconds (default: 5 minutes)
}

const CreditMonitor: React.FC<CreditMonitorProps> = ({
  lowCreditThreshold = 20,
  criticalCreditThreshold = 10,
  enableToastNotifications = true,
  enableBrowserNotifications = true,
  checkInterval = 5 * 60 * 1000, // 5 minutes
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const notifiedLevels = useRef<Set<string>>(new Set());
  const lastNotificationTime = useRef<number>(0);

  const { data: subscriptionResponse } = useSubscriptionStatus();
  const { data: isCreditsLow } = useCreditsLowCheck(user?.id, lowCreditThreshold / 100);

  const subscriptionData = subscriptionResponse?.data;
  const credits = subscriptionData?.credits;
  const hasActiveSubscription = subscriptionData?.hasActiveSubscription;

  // Request browser notification permission
  useEffect(() => {
    if (enableBrowserNotifications && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('✅ Credit notification permission granted');
        }
      });
    }
  }, [enableBrowserNotifications]);

  // Monitor credit levels
  useEffect(() => {
    if (!hasActiveSubscription || !credits) {
      return;
    }

    const currentTime = Date.now();
    const timeSinceLastNotification = currentTime - lastNotificationTime.current;
    const minTimeBetweenNotifications = 30 * 60 * 1000; // 30 minutes

    // Skip if we've notified recently
    if (timeSinceLastNotification < minTimeBetweenNotifications) {
      return;
    }

    const creditPercentage = credits.percentage;
    const currentCredits = credits.current;
    const allocatedCredits = credits.allocated;

    // Determine notification level
    let notificationLevel: 'critical' | 'low' | 'none' = 'none';
    let notificationKey = '';

    if (creditPercentage <= criticalCreditThreshold) {
      notificationLevel = 'critical';
      notificationKey = `critical-${Math.floor(creditPercentage / 5) * 5}`; // Group by 5% intervals
    } else if (creditPercentage <= lowCreditThreshold) {
      notificationLevel = 'low';
      notificationKey = `low-${Math.floor(creditPercentage / 10) * 10}`; // Group by 10% intervals
    }

    // Only notify if we haven't notified for this level recently
    if (notificationLevel !== 'none' && !notifiedLevels.current.has(notificationKey)) {
      // Mark as notified
      notifiedLevels.current.add(notificationKey);
      lastNotificationTime.current = currentTime;

      // Clear older notifications to prevent spam
      if (notifiedLevels.current.size > 5) {
        const oldestKeys = Array.from(notifiedLevels.current).slice(0, -3);
        oldestKeys.forEach(key => notifiedLevels.current.delete(key));
      }

      const notificationData = getNotificationData(
        notificationLevel,
        currentCredits,
        allocatedCredits,
        creditPercentage
      );

      // Show toast notification
      if (enableToastNotifications) {
        showToastNotification(notificationData);
      }

      // Show browser notification
      if (enableBrowserNotifications) {
        showBrowserNotification(notificationData);
      }
    }
  }, [
    hasActiveSubscription,
    credits,
    lowCreditThreshold,
    criticalCreditThreshold,
    enableToastNotifications,
    enableBrowserNotifications,
  ]);

  const getNotificationData = (
    level: 'critical' | 'low',
    currentCredits: number,
    allocatedCredits: number,
    percentage: number
  ) => {
    const isCritical = level === 'critical';
    
    return {
      title: isCritical ? '🚨 Critical: Credits Almost Depleted' : '⚠️ Low Credits Warning',
      message: isCritical
        ? `Only ${currentCredits.toLocaleString()} credits remaining (${percentage.toFixed(1)}%). Upgrade now to avoid service interruption.`
        : `You have ${currentCredits.toLocaleString()} credits remaining (${percentage.toFixed(1)}%). Consider upgrading your plan.`,
      type: isCritical ? 'error' : 'warning',
      actionText: isCritical ? 'Upgrade Now' : 'View Plans',
      actionUrl: '/subscription/plans',
      duration: isCritical ? 8000 : 6000,
    };
  };

  const showToastNotification = (data: any) => {
    const toastOptions = {
      duration: data.duration,
      style: {
        maxWidth: '400px',
      },
    };

    const toastContent = (
      <div className="flex items-start space-x-3">
        <div className="flex-1">
          <div className="font-medium text-gray-900 dark:text-white">
            {data.title}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
            {data.message}
          </div>
          <button
            onClick={() => {
              navigate(data.actionUrl);
              toast.dismiss();
            }}
            className="mt-2 text-sm font-medium text-[#19727F] hover:text-[#19727F]/80 underline"
          >
            {data.actionText}
          </button>
        </div>
      </div>
    );

    if (data.type === 'error') {
      toast.error(toastContent, toastOptions);
    } else {
      toast(toastContent, {
        ...toastOptions,
        icon: '⚠️',
      });
    }
  };

  const showBrowserNotification = (data: any) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(data.title, {
        body: data.message,
        icon: '/favicon.ico',
        tag: 'credit-warning',
        requireInteraction: data.type === 'error', // Require interaction for critical warnings
        actions: [
          {
            action: 'upgrade',
            title: data.actionText,
          },
          {
            action: 'dismiss',
            title: 'Dismiss',
          },
        ],
      });

      notification.onclick = () => {
        window.focus();
        navigate(data.actionUrl);
        notification.close();
      };

      // Auto-close after duration
      setTimeout(() => {
        notification.close();
      }, data.duration);
    }
  };

  // This component doesn't render anything visible
  return null;
};

export default CreditMonitor;
