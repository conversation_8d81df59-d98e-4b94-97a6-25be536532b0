import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import toast from 'react-hot-toast';
import { useSubscriptionStatus } from './useSubscription';
import { useAuth } from '../context/AuthContext';

export interface SubscriptionValidationOptions {
  requiredCredits?: number;
  requiredPlan?: 'free' | 'hobby' | 'pro';
  feature?: string;
  showToast?: boolean;
  redirectOnFail?: boolean;
  customErrorMessage?: string;
}

export interface SubscriptionValidationResult {
  isValid: boolean;
  reason?: 'no_subscription' | 'insufficient_credits' | 'plan_required' | 'subscription_expired';
  message?: string;
  actionUrl?: string;
  actionText?: string;
}

export const useSubscriptionValidation = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { data: subscriptionResponse, isLoading } = useSubscriptionStatus();

  const subscriptionData = subscriptionResponse?.data;
  const hasActiveSubscription = subscriptionData?.hasActiveSubscription;
  const subscription = subscriptionData?.subscription;
  const credits = subscriptionData?.credits;

  const validateAccess = useCallback((options: SubscriptionValidationOptions = {}): SubscriptionValidationResult => {
    const {
      requiredCredits,
      requiredPlan,
      feature,
      showToast = false,
      redirectOnFail = false,
      customErrorMessage,
    } = options;

    // Loading state
    if (isLoading) {
      return { isValid: false, reason: undefined, message: 'Loading subscription status...' };
    }

    // Check if subscription is active
    if (!hasActiveSubscription) {
      const result: SubscriptionValidationResult = {
        isValid: false,
        reason: 'no_subscription',
        message: customErrorMessage || `${feature ? `The ${feature} feature` : 'This feature'} requires an active subscription.`,
        actionUrl: '/subscription/plans',
        actionText: 'View Plans',
      };

      if (showToast) {
        toast.error(result.message!, {
          duration: 4000,
          action: {
            label: result.actionText!,
            onClick: () => navigate(result.actionUrl!),
          },
        });
      }

      if (redirectOnFail) {
        navigate(result.actionUrl!);
      }

      return result;
    }

    // Check if subscription is expired
    if (subscription && subscription.status !== 'active') {
      const result: SubscriptionValidationResult = {
        isValid: false,
        reason: 'subscription_expired',
        message: customErrorMessage || 'Your subscription has expired. Please renew to continue using premium features.',
        actionUrl: '/subscription/plans',
        actionText: 'Renew Subscription',
      };

      if (showToast) {
        toast.error(result.message!, {
          duration: 4000,
          action: {
            label: result.actionText!,
            onClick: () => navigate(result.actionUrl!),
          },
        });
      }

      if (redirectOnFail) {
        navigate(result.actionUrl!);
      }

      return result;
    }

    // Check plan requirements
    if (requiredPlan && subscription) {
      const planHierarchy = { free: 0, hobby: 1, pro: 2 };
      const currentPlanLevel = planHierarchy[subscription.name.toLowerCase() as keyof typeof planHierarchy] ?? 0;
      const requiredPlanLevel = planHierarchy[requiredPlan];

      if (currentPlanLevel < requiredPlanLevel) {
        const result: SubscriptionValidationResult = {
          isValid: false,
          reason: 'plan_required',
          message: customErrorMessage || `${feature ? `The ${feature} feature` : 'This feature'} requires the ${requiredPlan} plan or higher.`,
          actionUrl: '/subscription/plans',
          actionText: 'Upgrade Plan',
        };

        if (showToast) {
          toast.error(result.message!, {
            duration: 4000,
            action: {
              label: result.actionText!,
              onClick: () => navigate(result.actionUrl!),
            },
          });
        }

        if (redirectOnFail) {
          navigate(result.actionUrl!);
        }

        return result;
      }
    }

    // Check credit requirements
    if (requiredCredits && credits) {
      if (credits.current < requiredCredits) {
        const result: SubscriptionValidationResult = {
          isValid: false,
          reason: 'insufficient_credits',
          message: customErrorMessage || `You need ${requiredCredits.toLocaleString()} credits to use ${feature ? `the ${feature} feature` : 'this feature'}. You currently have ${credits.current.toLocaleString()} credits.`,
          actionUrl: '/subscription/plans',
          actionText: 'Upgrade Plan',
        };

        if (showToast) {
          toast.error(result.message!, {
            duration: 4000,
            action: {
              label: result.actionText!,
              onClick: () => navigate(result.actionUrl!),
            },
          });
        }

        if (redirectOnFail) {
          navigate(result.actionUrl!);
        }

        return result;
      }
    }

    // All checks passed
    return { isValid: true };
  }, [
    isLoading,
    hasActiveSubscription,
    subscription,
    credits,
    navigate,
  ]);

  const validateAndExecute = useCallback(
    async (
      action: () => Promise<void> | void,
      options: SubscriptionValidationOptions = {}
    ): Promise<boolean> => {
      const validation = validateAccess(options);
      
      if (validation.isValid) {
        try {
          await action();
          return true;
        } catch (error) {
          console.error('Action execution failed:', error);
          if (options.showToast) {
            toast.error('An error occurred while performing this action.');
          }
          return false;
        }
      }
      
      return false;
    },
    [validateAccess]
  );

  const getSubscriptionStatus = useCallback(() => {
    return {
      hasActiveSubscription,
      subscription,
      credits,
      isLoading,
    };
  }, [hasActiveSubscription, subscription, credits, isLoading]);

  const canAccessFeature = useCallback((options: SubscriptionValidationOptions = {}): boolean => {
    return validateAccess(options).isValid;
  }, [validateAccess]);

  const getUpgradeUrl = useCallback((reason?: string): string => {
    switch (reason) {
      case 'no_subscription':
      case 'subscription_expired':
      case 'plan_required':
      case 'insufficient_credits':
        return '/subscription/plans';
      default:
        return '/subscription';
    }
  }, []);

  return {
    validateAccess,
    validateAndExecute,
    canAccessFeature,
    getSubscriptionStatus,
    getUpgradeUrl,
    isLoading,
  };
};

export default useSubscriptionValidation;
