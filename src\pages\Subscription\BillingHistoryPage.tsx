import React from 'react';
import { useNavigate } from 'react-router';
import { BillingHistory } from '../../components/subscription';
import { useAuth } from '../../context/AuthContext';

const BillingHistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { provider } = useAuth();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Billing History
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            View and manage your {provider?.title || 'business'} billing history and invoices
          </p>
        </div>
        <button
          onClick={() => navigate('/subscription')}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Subscription
        </button>
      </div>

      {/* Billing History Component */}
      <BillingHistory 
        pageSize={15}
        showFilters={true}
        allowExport={true}
      />

      {/* Help Section */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Need Help with Billing?
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Common Questions
            </h4>
            <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <li>• How to download invoices</li>
              <li>• Understanding your billing cycle</li>
              <li>• Credit usage and allocation</li>
              <li>• Subscription plan changes</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              Contact Support
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Have questions about your billing? Our support team is here to help.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded text-[#19727F] bg-[#19727F]/10 hover:bg-[#19727F]/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
              >
                Email Support
              </button>
              <button
                onClick={() => window.open('https://help.dalti.app/billing', '_blank')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors dark:border-gray-600 dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
              >
                Help Center
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BillingHistoryPage;
