import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import { useCredits, useCreditUsageAnalytics } from '../../hooks/useSubscription';
import { CreditsUsageProps } from '../../types';
import { useAuth } from '../../context/AuthContext';
import { ErrorDisplay } from '../error/ErrorDisplay';

const CreditsUsage: React.FC<CreditsUsageProps> = ({
  showHistory = true,
  showAnalytics = false,
  warningThreshold = 20,
  className,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<30 | 7 | 1>(30);
  
  const { data: creditsResponse, isLoading, error, refetch } = useCredits(user?.id);
  const { data: analyticsData } = useCreditUsageAnalytics(user?.id, selectedPeriod);

  if (isLoading) {
    return (
      <div className={clsx(
        "rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6",
        className
      )}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4 dark:bg-gray-700"></div>
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4 dark:bg-gray-700"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-2 dark:bg-gray-700"></div>
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-4 bg-gray-200 rounded dark:bg-gray-700"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={clsx(
        "rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6",
        className
      )}>
        <ErrorDisplay 
          error={error} 
          title="Failed to load credits information"
          showRetry={true}
          onRetry={refetch}
          variant="card"
        />
      </div>
    );
  }

  if (!creditsResponse?.success || !creditsResponse.data) {
    return null;
  }

  const { credits, creditHistory } = creditsResponse.data;
  const isLowCredits = credits <= warningThreshold;

  const getCreditsColor = () => {
    if (credits <= warningThreshold) return 'text-red-600 dark:text-red-400';
    if (credits <= warningThreshold * 2) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  const getProgressColor = () => {
    if (credits <= warningThreshold) return 'bg-red-500';
    if (credits <= warningThreshold * 2) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // Calculate progress percentage (assuming max credits based on history)
  const maxCreditsFromHistory = Math.max(...creditHistory.map(entry => entry.balance), credits);
  const progressPercentage = maxCreditsFromHistory > 0 ? (credits / maxCreditsFromHistory) * 100 : 0;

  return (
    <div className={clsx(
      "rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            Credits Usage
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Track your credit balance and usage
          </p>
        </div>
        {isLowCredits && (
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-sm font-medium text-red-600 dark:text-red-400">
              Low Credits
            </span>
          </div>
        )}
      </div>

      {/* Current Credits */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Current Balance
          </span>
          <span className={clsx("text-2xl font-bold", getCreditsColor())}>
            {credits.toLocaleString()}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
          <div 
            className={clsx("h-3 rounded-full transition-all duration-300", getProgressColor())}
            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
          ></div>
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
          <span>0</span>
          <span>{maxCreditsFromHistory.toLocaleString()}</span>
        </div>
      </div>

      {/* Low Credits Warning */}
      {isLowCredits && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/10 dark:border-red-800/50">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-red-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                Running Low on Credits
              </h4>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                You have {credits} credits remaining. Consider upgrading your plan to avoid service interruption.
              </p>
              <button
                onClick={() => navigate('/subscription/plans')}
                className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30"
              >
                Upgrade Plan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Section */}
      {showAnalytics && analyticsData && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Usage Analytics
            </h4>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(Number(e.target.value) as 30 | 7 | 1)}
              className="text-xs border border-gray-300 rounded px-2 py-1 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300"
            >
              <option value={1}>Last 24 hours</option>
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
            </select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700/50">
              <p className="text-xs text-gray-500 dark:text-gray-400">Total Used</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {analyticsData.totalUsed?.toLocaleString() || 0}
              </p>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700/50">
              <p className="text-xs text-gray-500 dark:text-gray-400">Daily Average</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {analyticsData.averageDaily?.toFixed(1) || 0}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity */}
      {showHistory && creditHistory && creditHistory.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-4">
            Recent Activity
          </h4>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {creditHistory.slice(0, 10).map((entry, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                <div className="flex-1">
                  <p className="text-sm text-gray-900 dark:text-white">
                    {entry.reason}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(entry.date).toLocaleDateString()} at {new Date(entry.date).toLocaleTimeString()}
                  </p>
                </div>
                <div className="flex items-center">
                  <span className={clsx(
                    "text-sm font-medium mr-3",
                    entry.change > 0 
                      ? "text-green-600 dark:text-green-400" 
                      : "text-red-600 dark:text-red-400"
                  )}>
                    {entry.change > 0 ? '+' : ''}{entry.change.toLocaleString()}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 min-w-0">
                    Balance: {entry.balance.toLocaleString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          {creditHistory.length > 10 && (
            <div className="mt-4 text-center">
              <button
                onClick={() => navigate('/subscription/billing')}
                className="text-sm text-[#19727F] hover:text-[#19727F]/80 font-medium"
              >
                View Full History
              </button>
            </div>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="mt-6 flex gap-3">
        <button
          onClick={() => navigate('/subscription')}
          className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
        >
          Manage Subscription
        </button>
        {isLowCredits && (
          <button
            onClick={() => navigate('/subscription/plans')}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-lg text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors dark:border-orange-600 dark:text-orange-300 dark:bg-orange-900/20 dark:hover:bg-orange-900/30"
          >
            Upgrade Now
          </button>
        )}
      </div>
    </div>
  );
};

export default CreditsUsage;
