import React, { ReactNode } from 'react';
import { useNavigate } from 'react-router';
import { useSubscriptionStatus, useCheckCreditsAvailability } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';
import { Modal } from '../ui/modal';
import { Button } from '../ui/button/Button';

interface SubscriptionGuardProps {
  children: ReactNode;
  requiredCredits?: number;
  requiredPlan?: 'free' | 'hobby' | 'pro';
  feature?: string;
  fallback?: ReactNode;
  showUpgradeModal?: boolean;
  onUpgradeRequired?: () => void;
}

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  reason: 'no_subscription' | 'insufficient_credits' | 'plan_required';
  requiredCredits?: number;
  requiredPlan?: string;
  feature?: string;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  reason,
  requiredCredits,
  requiredPlan,
  feature,
}) => {
  const navigate = useNavigate();

  const getModalContent = () => {
    switch (reason) {
      case 'no_subscription':
        return {
          title: 'Subscription Required',
          message: `${feature ? `The ${feature} feature` : 'This feature'} requires an active subscription. Upgrade to access premium features and grow your business.`,
          actionText: 'View Plans',
          actionUrl: '/subscription/plans',
        };
      case 'insufficient_credits':
        return {
          title: 'Insufficient Credits',
          message: `You need ${requiredCredits?.toLocaleString()} credits to use ${feature ? `the ${feature} feature` : 'this feature'}. Upgrade your plan or purchase additional credits.`,
          actionText: 'Upgrade Plan',
          actionUrl: '/subscription/plans',
        };
      case 'plan_required':
        return {
          title: 'Plan Upgrade Required',
          message: `${feature ? `The ${feature} feature` : 'This feature'} requires the ${requiredPlan} plan or higher. Upgrade to unlock advanced features.`,
          actionText: 'Upgrade Plan',
          actionUrl: '/subscription/plans',
        };
      default:
        return {
          title: 'Upgrade Required',
          message: 'This feature requires a subscription upgrade.',
          actionText: 'View Plans',
          actionUrl: '/subscription/plans',
        };
    }
  };

  const content = getModalContent();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="max-w-md p-0"
      showCloseButton={false}
    >
      <div className="p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {content.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {content.message}
          </p>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            Maybe Later
          </Button>
          <Button
            onClick={() => {
              onClose();
              navigate(content.actionUrl);
            }}
            className="flex-1 bg-[#19727F] hover:bg-[#19727F]/90 text-white"
          >
            {content.actionText}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  children,
  requiredCredits,
  requiredPlan,
  feature,
  fallback,
  showUpgradeModal = true,
  onUpgradeRequired,
}) => {
  const { user } = useAuth();
  const { data: subscriptionResponse, isLoading } = useSubscriptionStatus();
  const [showModal, setShowModal] = React.useState(false);
  const [modalReason, setModalReason] = React.useState<'no_subscription' | 'insufficient_credits' | 'plan_required'>('no_subscription');

  const subscriptionData = subscriptionResponse?.data;
  const hasActiveSubscription = subscriptionData?.hasActiveSubscription;
  const subscription = subscriptionData?.subscription;
  const credits = subscriptionData?.credits;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#19727F]"></div>
      </div>
    );
  }

  // Check subscription requirements
  const checkAccess = (): { hasAccess: boolean; reason?: string } => {
    // Check if subscription is required
    if (!hasActiveSubscription) {
      return { hasAccess: false, reason: 'no_subscription' };
    }

    // Check plan requirements
    if (requiredPlan && subscription) {
      const planHierarchy = { free: 0, hobby: 1, pro: 2 };
      const currentPlanLevel = planHierarchy[subscription.name.toLowerCase() as keyof typeof planHierarchy] ?? 0;
      const requiredPlanLevel = planHierarchy[requiredPlan];

      if (currentPlanLevel < requiredPlanLevel) {
        return { hasAccess: false, reason: 'plan_required' };
      }
    }

    // Check credit requirements
    if (requiredCredits && credits) {
      if (credits.current < requiredCredits) {
        return { hasAccess: false, reason: 'insufficient_credits' };
      }
    }

    return { hasAccess: true };
  };

  const { hasAccess, reason } = checkAccess();

  // Handle access denied
  if (!hasAccess) {
    if (onUpgradeRequired) {
      onUpgradeRequired();
    }

    if (showUpgradeModal && reason) {
      const handleShowModal = () => {
        setModalReason(reason as any);
        setShowModal(true);
      };

      // Auto-show modal for critical features
      React.useEffect(() => {
        if (reason === 'insufficient_credits' || reason === 'plan_required') {
          handleShowModal();
        }
      }, [reason]);

      return (
        <>
          {fallback || (
            <div className="p-6 text-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {reason === 'no_subscription' && 'Subscription Required'}
                {reason === 'insufficient_credits' && 'Insufficient Credits'}
                {reason === 'plan_required' && 'Plan Upgrade Required'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {reason === 'no_subscription' && `${feature ? `The ${feature} feature` : 'This feature'} requires an active subscription.`}
                {reason === 'insufficient_credits' && `You need ${requiredCredits?.toLocaleString()} credits to access ${feature ? `the ${feature} feature` : 'this feature'}.`}
                {reason === 'plan_required' && `${feature ? `The ${feature} feature` : 'This feature'} requires the ${requiredPlan} plan or higher.`}
              </p>
              <Button
                onClick={handleShowModal}
                className="bg-[#19727F] hover:bg-[#19727F]/90 text-white"
              >
                {reason === 'no_subscription' && 'View Plans'}
                {reason === 'insufficient_credits' && 'Upgrade Plan'}
                {reason === 'plan_required' && 'Upgrade Plan'}
              </Button>
            </div>
          )}
          <UpgradeModal
            isOpen={showModal}
            onClose={() => setShowModal(false)}
            reason={modalReason}
            requiredCredits={requiredCredits}
            requiredPlan={requiredPlan}
            feature={feature}
          />
        </>
      );
    }

    return fallback || null;
  }

  // Access granted - render children
  return <>{children}</>;
};

export default SubscriptionGuard;
