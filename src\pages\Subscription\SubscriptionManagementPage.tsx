import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import { 
  SubscriptionStatusCard, 
  CreditsUsage, 
  SubscriptionManagement 
} from '../../components/subscription';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';

const SubscriptionManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { provider } = useAuth();
  const [showManagementModal, setShowManagementModal] = useState(false);
  const { data: subscriptionResponse } = useSubscriptionStatus();

  const subscriptionData = subscriptionResponse?.data;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Subscription Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your {provider?.title || 'business'} subscription, billing, and credits
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          onClick={() => navigate('/subscription/plans')}
          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
        >
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg mr-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/30 transition-colors">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">View Plans</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Compare & upgrade</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => navigate('/subscription/billing')}
          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
        >
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg mr-3 group-hover:bg-green-200 dark:group-hover:bg-green-900/30 transition-colors">
              <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Billing History</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">View invoices</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => setShowManagementModal(true)}
          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
        >
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg mr-3 group-hover:bg-purple-200 dark:group-hover:bg-purple-900/30 transition-colors">
              <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Settings</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Manage subscription</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => {
            // Handle support/help action
            window.open('mailto:<EMAIL>', '_blank');
          }}
          className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left group"
        >
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg mr-3 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/30 transition-colors">
              <svg className="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">Get Help</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Contact support</p>
            </div>
          </div>
        </button>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subscription Status */}
        <div className="lg:col-span-1">
          <SubscriptionStatusCard 
            showActions={true}
            compact={false}
          />
        </div>

        {/* Credits Usage */}
        <div className="lg:col-span-1">
          <CreditsUsage 
            showHistory={true}
            showAnalytics={false}
            warningThreshold={20}
          />
        </div>
      </div>

      {/* Additional Information */}
      {subscriptionData?.hasActiveSubscription && (
        <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800/50 rounded-lg p-6">
          <div className="flex items-start">
            <svg className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Subscription Information
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Your subscription will automatically renew on{' '}
                <strong>
                  {subscriptionData.subscription ? 
                    new Date(subscriptionData.subscription.endDate).toLocaleDateString() : 
                    'N/A'
                  }
                </strong>
                . You can cancel or modify your subscription at any time from the settings.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* No Active Subscription CTA */}
      {!subscriptionData?.hasActiveSubscription && (
        <div className="bg-gradient-to-r from-[#19727F] to-[#19727F]/80 rounded-lg p-8 text-white">
          <div className="max-w-3xl">
            <h2 className="text-2xl font-bold mb-2">
              Unlock Premium Features
            </h2>
            <p className="text-blue-100 mb-6">
              Upgrade to a paid plan to access advanced features, get more credits, 
              and grow your business with our comprehensive provider tools.
            </p>
            <div className="flex flex-wrap gap-4">
              <button
                onClick={() => navigate('/subscription/plans')}
                className="bg-white text-[#19727F] px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                View Plans & Pricing
              </button>
              <button
                onClick={() => setShowManagementModal(true)}
                className="border border-white/30 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/10 transition-colors"
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Management Modal */}
      <SubscriptionManagement
        isOpen={showManagementModal}
        onClose={() => setShowManagementModal(false)}
        initialTab="overview"
      />
    </div>
  );
};

export default SubscriptionManagementPage;
