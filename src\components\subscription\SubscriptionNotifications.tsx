import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useSubscriptionStatus, useSubscriptionExpirationCheck } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';

interface SubscriptionNotification {
  id: string;
  type: 'subscription_expiring' | 'subscription_expired' | 'low_credits' | 'critical_credits' | 'payment_failed';
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  isRead: boolean;
}

interface SubscriptionNotificationsProps {
  onNotification?: (notification: SubscriptionNotification) => void;
}

const SubscriptionNotifications: React.FC<SubscriptionNotificationsProps> = ({
  onNotification,
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const { data: subscriptionResponse } = useSubscriptionStatus();
  const { data: isExpiringSoon } = useSubscriptionExpirationCheck(7); // 7 days threshold

  const subscriptionData = subscriptionResponse?.data;
  const subscription = subscriptionData?.subscription;
  const credits = subscriptionData?.credits;
  const hasActiveSubscription = subscriptionData?.hasActiveSubscription;

  // Check for subscription expiration notifications
  useEffect(() => {
    if (!hasActiveSubscription || !subscription) return;

    const daysRemaining = subscription.daysRemaining;
    
    // Subscription expiring soon (7, 3, 1 days)
    if (daysRemaining <= 7 && daysRemaining > 0) {
      const notification: SubscriptionNotification = {
        id: `subscription-expiring-${daysRemaining}`,
        type: 'subscription_expiring',
        title: `Subscription Expires in ${daysRemaining} Day${daysRemaining === 1 ? '' : 's'}`,
        message: `Your ${subscription.name} subscription will expire on ${new Date(subscription.endDate).toLocaleDateString()}. Renew now to avoid service interruption.`,
        actionUrl: '/subscription/plans',
        actionText: 'Renew Subscription',
        priority: daysRemaining <= 1 ? 'critical' : daysRemaining <= 3 ? 'high' : 'medium',
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      if (onNotification) {
        onNotification(notification);
      }
    }

    // Subscription expired
    if (daysRemaining <= 0) {
      const notification: SubscriptionNotification = {
        id: 'subscription-expired',
        type: 'subscription_expired',
        title: 'Subscription Expired',
        message: `Your ${subscription.name} subscription has expired. Renew now to restore access to premium features.`,
        actionUrl: '/subscription/plans',
        actionText: 'Renew Now',
        priority: 'critical',
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      if (onNotification) {
        onNotification(notification);
      }
    }
  }, [hasActiveSubscription, subscription, onNotification]);

  // Check for credit-related notifications
  useEffect(() => {
    if (!hasActiveSubscription || !credits) return;

    const creditPercentage = credits.percentage;
    const currentCredits = credits.current;

    // Critical credits (≤ 10%)
    if (creditPercentage <= 10) {
      const notification: SubscriptionNotification = {
        id: `critical-credits-${Math.floor(creditPercentage)}`,
        type: 'critical_credits',
        title: '🚨 Critical: Credits Almost Depleted',
        message: `Only ${currentCredits.toLocaleString()} credits remaining (${creditPercentage.toFixed(1)}%). Upgrade immediately to avoid service interruption.`,
        actionUrl: '/subscription/plans',
        actionText: 'Upgrade Now',
        priority: 'critical',
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      if (onNotification) {
        onNotification(notification);
      }
    }
    // Low credits (≤ 20%)
    else if (creditPercentage <= 20) {
      const notification: SubscriptionNotification = {
        id: `low-credits-${Math.floor(creditPercentage / 5) * 5}`,
        type: 'low_credits',
        title: '⚠️ Low Credits Warning',
        message: `You have ${currentCredits.toLocaleString()} credits remaining (${creditPercentage.toFixed(1)}%). Consider upgrading your plan to ensure uninterrupted service.`,
        actionUrl: '/subscription/plans',
        actionText: 'View Plans',
        priority: 'medium',
        timestamp: new Date().toISOString(),
        isRead: false,
      };

      if (onNotification) {
        onNotification(notification);
      }
    }
  }, [hasActiveSubscription, credits, onNotification]);

  // This component doesn't render anything visible
  return null;
};

// Helper function to get notification icon based on type
export const getSubscriptionNotificationIcon = (type: string) => {
  switch (type) {
    case 'subscription_expiring':
      return (
        <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    case 'subscription_expired':
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      );
    case 'low_credits':
      return (
        <svg className="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      );
    case 'critical_credits':
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      );
    case 'payment_failed':
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    default:
      return (
        <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
  }
};

// Helper function to get notification color based on type
export const getSubscriptionNotificationColor = (type: string) => {
  switch (type) {
    case 'subscription_expiring':
      return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
    case 'subscription_expired':
      return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
    case 'low_credits':
      return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
    case 'critical_credits':
      return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
    case 'payment_failed':
      return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
    default:
      return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
  }
};

export default SubscriptionNotifications;
