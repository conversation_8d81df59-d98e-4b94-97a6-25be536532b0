import React, { useState } from 'react';
import { Modal } from '../ui/modal';
import { useCancelSubscription, useSubscriptionStatus } from '../../hooks/useSubscription';
import { Button } from '../ui/button/Button';
import { ErrorDisplay } from '../error/ErrorDisplay';

interface SubscriptionCancellationProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  subscriptionId?: string;
}

const SubscriptionCancellation: React.FC<SubscriptionCancellationProps> = ({
  isOpen,
  onClose,
  onSuccess,
  subscriptionId,
}) => {
  const [step, setStep] = useState<'confirm' | 'feedback' | 'processing'>('confirm');
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');
  const [showRetentionOffer, setShowRetentionOffer] = useState(false);

  const { data: subscriptionResponse } = useSubscriptionStatus();
  const cancelSubscription = useCancelSubscription();

  const effectiveSubscriptionId = subscriptionId || subscriptionResponse?.data?.subscription?.id;

  const handleCancel = () => {
    setStep('confirm');
    setCancelReason('');
    setCancelFeedback('');
    setShowRetentionOffer(false);
    onClose();
  };

  const handleConfirmCancellation = () => {
    // Show retention offer for certain reasons
    if (cancelReason === 'too_expensive' || cancelReason === 'not_using') {
      setShowRetentionOffer(true);
    } else {
      setStep('feedback');
    }
  };

  const handleProceedWithCancellation = async () => {
    if (!effectiveSubscriptionId) return;

    setStep('processing');

    try {
      await cancelSubscription.mutateAsync({
        subscriptionId: effectiveSubscriptionId,
        reason: cancelReason,
        feedback: cancelFeedback,
      });

      if (onSuccess) {
        onSuccess();
      }
      onClose();
    } catch (error) {
      setStep('feedback');
      // Error is handled by the hook
    }
  };

  const cancelReasons = [
    { value: 'too_expensive', label: 'Too expensive for my budget' },
    { value: 'not_using', label: 'Not using the service enough' },
    { value: 'missing_features', label: 'Missing features I need' },
    { value: 'technical_issues', label: 'Experiencing technical issues' },
    { value: 'switching_provider', label: 'Switching to another provider' },
    { value: 'business_closure', label: 'Closing my business' },
    { value: 'temporary_pause', label: 'Need a temporary break' },
    { value: 'other', label: 'Other reason' },
  ];

  const renderConfirmStep = () => (
    <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-red-900/20">
          <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Cancel Your Subscription
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          We're sorry to see you go. Help us understand why you're leaving.
        </p>
      </div>

      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            What's your main reason for cancelling? *
          </label>
          <div className="space-y-2">
            {cancelReasons.map((reason) => (
              <label key={reason.value} className="flex items-center">
                <input
                  type="radio"
                  name="cancelReason"
                  value={reason.value}
                  checked={cancelReason === reason.value}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="h-4 w-4 text-[#19727F] focus:ring-[#19727F] border-gray-300 dark:border-gray-600"
                />
                <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                  {reason.label}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={handleCancel}
          className="flex-1"
        >
          Keep Subscription
        </Button>
        <Button
          onClick={handleConfirmCancellation}
          disabled={!cancelReason}
          className="flex-1 bg-red-600 hover:bg-red-700 text-white"
        >
          Continue
        </Button>
      </div>
    </div>
  );

  const renderRetentionOffer = () => (
    <div className="p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-blue-900/20">
          <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Wait! We Have an Offer for You
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Before you go, we'd like to offer you a special deal to keep you with us.
        </p>
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 mb-6">
        <div className="text-center">
          <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            50% Off Your Next 3 Months
          </h4>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Stay with us and get 50% off your subscription for the next 3 months. 
            That's significant savings while you continue to grow your business.
          </p>
          <div className="flex items-center justify-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <span>✓ Keep all your data</span>
            <span>✓ No setup required</span>
            <span>✓ Cancel anytime</span>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          onClick={() => setStep('feedback')}
          variant="outline"
          className="flex-1"
        >
          No Thanks, Cancel
        </Button>
        <Button
          onClick={() => {
            // Handle accepting the offer
            // This would typically redirect to a special checkout or update the subscription
            onClose();
          }}
          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
        >
          Accept Offer
        </Button>
      </div>
    </div>
  );

  const renderFeedbackStep = () => (
    <div className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Final Step
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Your feedback helps us improve our service for everyone.
        </p>
      </div>

      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            How could we have done better? (Optional)
          </label>
          <textarea
            value={cancelFeedback}
            onChange={(e) => setCancelFeedback(e.target.value)}
            rows={4}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300"
            placeholder="Share your thoughts on how we can improve our service..."
          />
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800/50 rounded-lg p-4">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Important Information
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Your subscription will remain active until the end of your current billing period. 
                You'll continue to have access to all features until then.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={() => setStep('confirm')}
          className="flex-1"
        >
          Go Back
        </Button>
        <Button
          onClick={handleProceedWithCancellation}
          disabled={cancelSubscription.isPending}
          className="flex-1 bg-red-600 hover:bg-red-700 text-white"
        >
          {cancelSubscription.isPending ? 'Cancelling...' : 'Cancel Subscription'}
        </Button>
      </div>
    </div>
  );

  const renderProcessingStep = () => (
    <div className="p-6 text-center">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-gray-800">
        <svg className="animate-spin w-8 h-8 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Processing Cancellation
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Please wait while we process your cancellation request...
      </p>
    </div>
  );

  const renderContent = () => {
    if (cancelSubscription.error) {
      return (
        <div className="p-6">
          <ErrorDisplay 
            error={cancelSubscription.error}
            title="Failed to cancel subscription"
            showRetry={true}
            onRetry={() => setStep('feedback')}
            variant="card"
          />
        </div>
      );
    }

    switch (step) {
      case 'confirm':
        return renderConfirmStep();
      case 'feedback':
        return showRetentionOffer ? renderRetentionOffer() : renderFeedbackStep();
      case 'processing':
        return renderProcessingStep();
      default:
        return renderConfirmStep();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={step === 'processing' ? () => {} : handleCancel}
      className="max-w-md p-0"
      showCloseButton={step !== 'processing'}
    >
      {renderContent()}
    </Modal>
  );
};

export default SubscriptionCancellation;
