import React from 'react';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import { SubscriptionStatusCardProps } from '../../types';
import { ErrorDisplay } from '../error/ErrorDisplay';

const SubscriptionStatusCard: React.FC<SubscriptionStatusCardProps> = ({
  className,
  showActions = true,
  compact = false,
}) => {
  const navigate = useNavigate();
  const { data: subscriptionResponse, isLoading, error, refetch } = useSubscriptionStatus();

  if (isLoading) {
    return (
      <div className={clsx(
        "rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6",
        className
      )}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4 dark:bg-gray-700"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-4 dark:bg-gray-700"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-2 dark:bg-gray-700"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 dark:bg-gray-700"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={clsx(
        "rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6",
        className
      )}>
        <ErrorDisplay 
          error={error} 
          title="Failed to load subscription status"
          showRetry={true}
          onRetry={refetch}
          variant="card"
        />
      </div>
    );
  }

  if (!subscriptionResponse?.success) {
    return null;
  }

  const { data: subscriptionData } = subscriptionResponse;
  const { hasActiveSubscription, subscription, credits } = subscriptionData;

  // No active subscription state
  if (!hasActiveSubscription) {
    return (
      <div className={clsx(
        "rounded-2xl border border-yellow-200 bg-yellow-50 p-5 dark:border-yellow-800/50 dark:bg-yellow-900/10 lg:p-6",
        className
      )}>
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <div className="p-3 rounded-lg bg-yellow-500 text-white mr-4">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                No Active Subscription
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Upgrade to a paid plan to access premium features and credits
              </p>
            </div>
          </div>
        </div>
        
        {showActions && (
          <div className="mt-4 flex gap-3">
            <button
              onClick={() => navigate('/subscription/plans')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              View Plans
            </button>
            <button
              onClick={() => navigate('/subscription')}
              className="inline-flex items-center px-4 py-2 border border-yellow-300 text-sm font-medium rounded-lg text-yellow-700 bg-transparent hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/20"
            >
              Learn More
            </button>
          </div>
        )}
      </div>
    );
  }

  // Active subscription state
  if (!subscription) {
    return null;
  }

  const isExpiringSoon = subscription.daysRemaining <= 7;
  const isLowCredits = credits && credits.percentage <= 20;

  const getStatusColor = () => {
    if (subscription.status === 'active') {
      if (isExpiringSoon) return 'border-orange-200 bg-orange-50 dark:border-orange-800/50 dark:bg-orange-900/10';
      return 'border-green-200 bg-green-50 dark:border-green-800/50 dark:bg-green-900/10';
    }
    return 'border-red-200 bg-red-50 dark:border-red-800/50 dark:bg-red-900/10';
  };

  const getStatusIcon = () => {
    if (subscription.status === 'active') {
      return (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    return (
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    );
  };

  const getStatusIconColor = () => {
    if (subscription.status === 'active') {
      if (isExpiringSoon) return 'bg-orange-500';
      return 'bg-green-500';
    }
    return 'bg-red-500';
  };

  return (
    <div className={clsx(
      "rounded-2xl border p-5 lg:p-6",
      getStatusColor(),
      className
    )}>
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center">
          <div className={clsx("p-3 rounded-lg text-white mr-4", getStatusIconColor())}>
            {getStatusIcon()}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
              {subscription.name}
            </h3>
            <div className="flex items-center mt-1">
              <span className={clsx(
                "inline-flex items-center px-2 py-0.5 rounded text-xs font-medium capitalize",
                subscription.status === 'active' 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              )}>
                {subscription.status}
              </span>
              {isExpiringSoon && (
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
                  Expires Soon
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Details */}
      {!compact && (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Price</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white mt-1">
              {subscription.priceFormatted}/{subscription.interval}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Next Billing</p>
            <p className="text-lg font-semibold text-gray-900 dark:text-white mt-1">
              {new Date(subscription.endDate).toLocaleDateString()}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Days Remaining</p>
            <p className={clsx(
              "text-lg font-semibold mt-1",
              isExpiringSoon 
                ? "text-orange-600 dark:text-orange-400" 
                : "text-gray-900 dark:text-white"
            )}>
              {subscription.daysRemaining} days
            </p>
          </div>
        </div>
      )}

      {/* Credits Section */}
      {credits && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Usage</h4>
            {isLowCredits && (
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                Low Credits
              </span>
            )}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mb-2">
            <div 
              className={clsx(
                "h-2 rounded-full transition-all duration-300",
                isLowCredits 
                  ? "bg-red-500" 
                  : credits.percentage > 50 
                    ? "bg-green-500" 
                    : "bg-yellow-500"
              )}
              style={{ width: `${credits.percentage}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {credits.current.toLocaleString()} / {credits.allocated.toLocaleString()} credits remaining
          </p>
        </div>
      )}

      {/* Actions */}
      {showActions && (
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => navigate('/subscription')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
          >
            Manage Subscription
          </button>
          <button
            onClick={() => navigate('/subscription/billing')}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700"
          >
            View Billing
          </button>
          {isLowCredits && (
            <button
              onClick={() => navigate('/subscription/plans')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              Upgrade Plan
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default SubscriptionStatusCard;
