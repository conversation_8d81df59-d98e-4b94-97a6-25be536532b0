import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router';
import { useSubscriptionValidation, SubscriptionValidationOptions } from '../../hooks/useSubscriptionValidation';

interface SubscriptionProtectedRouteProps extends SubscriptionValidationOptions {
  children: ReactNode;
  fallbackComponent?: ReactNode;
  redirectTo?: string;
  allowFreeAccess?: boolean; // Allow access even without subscription
}

const SubscriptionProtectedRoute: React.FC<SubscriptionProtectedRouteProps> = ({
  children,
  fallbackComponent,
  redirectTo = '/subscription/plans',
  allowFreeAccess = false,
  ...validationOptions
}) => {
  const location = useLocation();
  const { validateAccess, isLoading } = useSubscriptionValidation();

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#19727F] mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Checking subscription status...</p>
        </div>
      </div>
    );
  }

  // If free access is allowed, render children without validation
  if (allowFreeAccess) {
    return <>{children}</>;
  }

  // Validate subscription access
  const validation = validateAccess(validationOptions);

  // If validation fails, handle accordingly
  if (!validation.isValid) {
    // If a custom fallback component is provided, use it
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    // Otherwise, redirect to the specified route with state
    return (
      <Navigate
        to={redirectTo}
        state={{
          from: location.pathname,
          reason: validation.reason,
          message: validation.message,
          requiredCredits: validationOptions.requiredCredits,
          requiredPlan: validationOptions.requiredPlan,
          feature: validationOptions.feature,
        }}
        replace
      />
    );
  }

  // Validation passed, render children
  return <>{children}</>;
};

export default SubscriptionProtectedRoute;
