import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SubscriptionService } from '../services/subscription.service';
import {
  CheckoutRequest,
  SubscriptionCancellationRequest,
  SubscriptionUpgradeRequest,
  SubscriptionDowngradeRequest,
} from '../types';
import { SubscriptionErrorLogger } from '../lib/subscription-error-utils';
import { ErrorLogger } from '../lib/error-utils';
import { useAuth } from '../context/AuthContext';
import toast from 'react-hot-toast';

/**
 * Hook for fetching subscription status
 */
export const useSubscriptionStatus = () => {
  return useQuery({
    queryKey: ['subscription', 'status'],
    queryFn: () => SubscriptionService.getSubscriptionStatus(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_status');
    },
  });
};

/**
 * Hook for fetching available subscription plans (authenticated)
 */
export const useSubscriptionPlans = () => {
  return useQuery({
    queryKey: ['subscription', 'plans'],
    queryFn: () => SubscriptionService.getAvailablePlans(),
    staleTime: 10 * 60 * 1000, // 10 minutes (plans don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_plans');
    },
  });
};

/**
 * Hook for fetching external subscription plans
 */
export const useExternalSubscriptionPlans = () => {
  return useQuery({
    queryKey: ['subscription', 'external-plans'],
    queryFn: () => SubscriptionService.getExternalSubscriptionPlans(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_external_plans');
    },
  });
};

/**
 * Hook for fetching user credits
 */
export const useCredits = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['subscription', 'credits'],
    queryFn: () => SubscriptionService.getUserCredits(),
    enabled: !!user,
    staleTime: 1 * 60 * 1000, // 1 minute (credits change frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logCreditError(error, user?.id!);
    },
  });
};

/**
 * Hook for fetching credit history
 */
export const useCreditHistory = () => {
  return useQuery({
    queryKey: ['subscription', 'credit-history'],
    queryFn: () => SubscriptionService.getCreditHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_credit_history');
    },
  });
};

/**
 * Hook for direct subscription to a plan
 */
export const useSubscribeToPlan = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (planId: string) => SubscriptionService.subscribeToPlan(planId),
    onSuccess: () => {
      // Invalidate subscription-related queries
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      toast.success('Successfully subscribed to plan!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to subscribe to plan';
      SubscriptionErrorLogger.logSubscriptionError(error, user?.id, 'subscribe');
      toast.error(message);
    },
  });
};

/**
 * Hook for creating external checkout session
 */
export const useCreateCheckout = () => {
  const { user, provider } = useAuth();

  return useMutation({
    mutationFn: (data: CheckoutRequest) => SubscriptionService.createCheckoutSession(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create checkout session';
      SubscriptionErrorLogger.logCheckoutError(error, '', user?.id || '');
      toast.error(message);
    },
  });
};

/**
 * Hook for creating subscription checkout session
 */
export const useCreateSubscriptionCheckout = () => {
  const { user, provider } = useAuth();

  return useMutation({
    mutationFn: (data: CheckoutRequest) => SubscriptionService.createSubscriptionCheckout(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create subscription checkout';
      SubscriptionErrorLogger.logCheckoutError(error, '', user?.id || '');
      toast.error(message);
    },
  });
};

/**
 * Hook for creating checkout with provider context
 */
export const useCreateCheckoutWithContext = () => {
  const { user, provider } = useAuth();

  return useMutation({
    mutationFn: ({ planId, returnUrl, cancelUrl }: { 
      planId: string; 
      returnUrl?: string; 
      cancelUrl?: string; 
    }) => {
      if (!user?.email || !provider?.title) {
        throw new Error('User or provider information not available');
      }
      
      return SubscriptionService.createCheckoutWithContext(
        planId,
        user.email,
        provider.title,
        returnUrl,
        cancelUrl
      );
    },
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create checkout session';
      SubscriptionErrorLogger.logCheckoutError(error, '', user?.id || '');
      toast.error(message);
    },
  });
};

/**
 * Hook for cancelling subscription
 */
export const useCancelSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionCancellationRequest) => 
      SubscriptionService.cancelSubscription(data),
    onSuccess: () => {
      // Invalidate subscription-related queries
      queryClient.invalidateQueries({ queryKey: ['subscription', 'status'] });
      queryClient.invalidateQueries({ queryKey: ['subscription', 'billing-history'] });
      
      toast.success('Subscription cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to cancel subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'cancel_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for upgrading subscription
 */
export const useUpgradeSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionUpgradeRequest) => 
      SubscriptionService.upgradeSubscription(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout for upgrade
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to upgrade subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'upgrade_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for downgrading subscription
 */
export const useDowngradeSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionDowngradeRequest) => 
      SubscriptionService.downgradeSubscription(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout for downgrade
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to downgrade subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'downgrade_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for checking credits availability
 */
export const useCheckCreditsAvailability = () => {
  const { user } = useAuth();

  return useMutation({
    mutationFn: ({ requiredCredits }: { requiredCredits: number }) => {
      if (!user?.id) {
        throw new Error('User ID not available');
      }
      return SubscriptionService.checkCreditsAvailability(user.id, requiredCredits);
    },
    onError: (error: any) => {
      SubscriptionErrorLogger.logCreditError(error, user?.id || '');
    },
  });
};

/**
 * Hook for validating feature access
 */
export const useValidateFeatureAccess = () => {
  return useMutation({
    mutationFn: ({ feature }: { feature: string }) => 
      SubscriptionService.validateFeatureAccess(feature),
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'validate_feature_access'
      );
    },
  });
};

/**
 * Hook for getting credit usage analytics
 */
export const useCreditUsageAnalytics = (userId?: string, days: number = 30) => {
  const { user } = useAuth();
  const effectiveUserId = userId || user?.id;

  return useQuery({
    queryKey: ['subscription', 'credit-analytics', effectiveUserId, days],
    queryFn: () => SubscriptionService.getCreditUsageAnalytics(effectiveUserId!, days),
    enabled: !!effectiveUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchCreditAnalytics' });
    },
  });
};

/**
 * Hook for checking if subscription is expiring soon
 */
export const useSubscriptionExpirationCheck = (daysThreshold: number = 7) => {
  return useQuery({
    queryKey: ['subscription', 'expiration-check', daysThreshold],
    queryFn: () => SubscriptionService.isSubscriptionExpiringSoon(daysThreshold),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 1,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'checkSubscriptionExpiration' });
    },
  });
};

/**
 * Hook for checking if credits are running low
 */
export const useCreditsLowCheck = (userId?: string, threshold: number = 0.2) => {
  const { user } = useAuth();
  const effectiveUserId = userId || user?.id;

  return useQuery({
    queryKey: ['subscription', 'credits-low-check', effectiveUserId, threshold],
    queryFn: () => SubscriptionService.areCreditsLow(effectiveUserId!, threshold),
    enabled: !!effectiveUserId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'checkCreditsLow' });
    },
  });
};

/**
 * Hook for updating user credits
 */
export const useUpdateCredits = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: any) => SubscriptionService.updateUserCredits(data),
    onSuccess: () => {
      // Invalidate credits-related queries
      queryClient.invalidateQueries({ queryKey: ['subscription', 'credits'] });
      toast.success('Credits updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update credits';
      SubscriptionErrorLogger.logCreditError(error, user?.id!);
      toast.error(message);
    },
  });
};

/**
 * Hook for updating subscription
 */
export const useUpdateSubscription = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: any) => SubscriptionService.updateSubscription(data),
    onSuccess: () => {
      // Invalidate subscription-related queries
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
      toast.success('Subscription updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to update subscription';
      SubscriptionErrorLogger.logSubscriptionError(error, user?.id, 'update');
      toast.error(message);
    },
  });
};
