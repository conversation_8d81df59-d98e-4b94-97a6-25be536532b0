import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SubscriptionService } from '../services/subscription.service';
import {
  CheckoutRequest,
  SubscriptionCancellationRequest,
  SubscriptionUpgradeRequest,
  SubscriptionDowngradeRequest,
} from '../types';
import { SubscriptionErrorLogger } from '../lib/subscription-error-utils';
import { ErrorLogger } from '../lib/error-utils';
import { useAuth } from '../context/AuthContext';
import toast from 'react-hot-toast';

/**
 * Hook for fetching subscription status
 */
export const useSubscriptionStatus = () => {
  return useQuery({
    queryKey: ['subscription', 'status'],
    queryFn: () => SubscriptionService.getSubscriptionStatus(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_status');
    },
  });
};

/**
 * Hook for fetching available subscription plans
 */
export const useSubscriptionPlans = () => {
  return useQuery({
    queryKey: ['subscription', 'plans'],
    queryFn: () => SubscriptionService.getAvailablePlans(),
    staleTime: 10 * 60 * 1000, // 10 minutes (plans don't change often)
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_plans');
    },
  });
};

/**
 * Hook for fetching user credits
 */
export const useCredits = (userId?: string) => {
  const { user } = useAuth();
  const effectiveUserId = userId || user?.id;

  return useQuery({
    queryKey: ['subscription', 'credits', effectiveUserId],
    queryFn: () => SubscriptionService.getUserCredits(effectiveUserId!),
    enabled: !!effectiveUserId,
    staleTime: 1 * 60 * 1000, // 1 minute (credits change frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logCreditError(error, effectiveUserId!);
    },
  });
};

/**
 * Hook for fetching billing history
 */
export const useBillingHistory = () => {
  return useQuery({
    queryKey: ['subscription', 'billing-history'],
    queryFn: () => SubscriptionService.getBillingHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(error, undefined, 'fetch_billing_history');
    },
  });
};

/**
 * Hook for creating checkout session
 */
export const useCreateCheckout = () => {
  const { user, provider } = useAuth();

  return useMutation({
    mutationFn: (data: CheckoutRequest) => SubscriptionService.createCheckoutSession(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create checkout session';
      SubscriptionErrorLogger.logCheckoutError(error, '', user?.id || '');
      toast.error(message);
    },
  });
};

/**
 * Hook for creating checkout with provider context
 */
export const useCreateCheckoutWithContext = () => {
  const { user, provider } = useAuth();

  return useMutation({
    mutationFn: ({ planId, returnUrl, cancelUrl }: { 
      planId: string; 
      returnUrl?: string; 
      cancelUrl?: string; 
    }) => {
      if (!user?.email || !provider?.title) {
        throw new Error('User or provider information not available');
      }
      
      return SubscriptionService.createCheckoutWithContext(
        planId,
        user.email,
        provider.title,
        returnUrl,
        cancelUrl
      );
    },
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to create checkout session';
      SubscriptionErrorLogger.logCheckoutError(error, '', user?.id || '');
      toast.error(message);
    },
  });
};

/**
 * Hook for cancelling subscription
 */
export const useCancelSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionCancellationRequest) => 
      SubscriptionService.cancelSubscription(data),
    onSuccess: () => {
      // Invalidate subscription-related queries
      queryClient.invalidateQueries({ queryKey: ['subscription', 'status'] });
      queryClient.invalidateQueries({ queryKey: ['subscription', 'billing-history'] });
      
      toast.success('Subscription cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to cancel subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'cancel_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for upgrading subscription
 */
export const useUpgradeSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionUpgradeRequest) => 
      SubscriptionService.upgradeSubscription(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout for upgrade
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to upgrade subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'upgrade_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for downgrading subscription
 */
export const useDowngradeSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionDowngradeRequest) => 
      SubscriptionService.downgradeSubscription(data),
    onSuccess: (response) => {
      if (response.success && response.data.sessionUrl) {
        // Redirect to checkout for downgrade
        window.location.href = response.data.sessionUrl;
      }
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to downgrade subscription';
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'downgrade_subscription'
      );
      toast.error(message);
    },
  });
};

/**
 * Hook for checking credits availability
 */
export const useCheckCreditsAvailability = () => {
  const { user } = useAuth();

  return useMutation({
    mutationFn: ({ requiredCredits }: { requiredCredits: number }) => {
      if (!user?.id) {
        throw new Error('User ID not available');
      }
      return SubscriptionService.checkCreditsAvailability(user.id, requiredCredits);
    },
    onError: (error: any) => {
      SubscriptionErrorLogger.logCreditError(error, user?.id || '');
    },
  });
};

/**
 * Hook for validating feature access
 */
export const useValidateFeatureAccess = () => {
  return useMutation({
    mutationFn: ({ feature }: { feature: string }) => 
      SubscriptionService.validateFeatureAccess(feature),
    onError: (error: any) => {
      SubscriptionErrorLogger.logSubscriptionError(
        error, 
        undefined, 
        'validate_feature_access'
      );
    },
  });
};

/**
 * Hook for getting credit usage analytics
 */
export const useCreditUsageAnalytics = (userId?: string, days: number = 30) => {
  const { user } = useAuth();
  const effectiveUserId = userId || user?.id;

  return useQuery({
    queryKey: ['subscription', 'credit-analytics', effectiveUserId, days],
    queryFn: () => SubscriptionService.getCreditUsageAnalytics(effectiveUserId!, days),
    enabled: !!effectiveUserId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchCreditAnalytics' });
    },
  });
};

/**
 * Hook for checking if subscription is expiring soon
 */
export const useSubscriptionExpirationCheck = (daysThreshold: number = 7) => {
  return useQuery({
    queryKey: ['subscription', 'expiration-check', daysThreshold],
    queryFn: () => SubscriptionService.isSubscriptionExpiringSoon(daysThreshold),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 1,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'checkSubscriptionExpiration' });
    },
  });
};

/**
 * Hook for checking if credits are running low
 */
export const useCreditsLowCheck = (userId?: string, threshold: number = 0.2) => {
  const { user } = useAuth();
  const effectiveUserId = userId || user?.id;

  return useQuery({
    queryKey: ['subscription', 'credits-low-check', effectiveUserId, threshold],
    queryFn: () => SubscriptionService.areCreditsLow(effectiveUserId!, threshold),
    enabled: !!effectiveUserId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'checkCreditsLow' });
    },
  });
};
