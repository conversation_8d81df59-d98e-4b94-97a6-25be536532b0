import React from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from '../../context/AuthContext';

const SubscriptionCancel: React.FC = () => {
  const navigate = useNavigate();
  const { provider } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {/* Cancel Icon */}
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-yellow-100 dark:bg-yellow-900/20 mb-6">
            <svg className="h-12 w-12 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Subscription Cancelled
          </h1>

          {/* Message */}
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            Your subscription process was cancelled. No charges have been made to your account.
          </p>

          {/* Business Name */}
          {provider?.title && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400">For business</p>
              <p className="font-semibold text-gray-900 dark:text-white">{provider.title}</p>
            </div>
          )}

          {/* What Happened */}
          <div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800/50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              What Happened?
            </h3>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 text-left">
              You cancelled the subscription process before completing payment. 
              Your account remains unchanged and no charges were processed.
            </p>
          </div>

          {/* Still Interested */}
          <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800/50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              Still Interested?
            </h3>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1 text-left">
              <li>• Try again with the same plan</li>
              <li>• Compare different subscription options</li>
              <li>• Contact our sales team for assistance</li>
              <li>• Continue with the free plan</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => navigate('/subscription/plans')}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              View Plans Again
            </button>
            
            <button
              onClick={() => navigate('/subscription')}
              className="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              Subscription Dashboard
            </button>

            <button
              onClick={() => navigate('/')}
              className="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              Go to Dashboard
            </button>
          </div>

          {/* Help Section */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Need Help?
            </h4>
            <div className="flex flex-col space-y-2">
              <button
                onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                className="text-sm text-[#19727F] hover:text-[#19727F]/80 font-medium"
              >
                Contact Sales Team
              </button>
              <button
                onClick={() => window.open('https://calendly.com/dalti-demo', '_blank')}
                className="text-sm text-[#19727F] hover:text-[#19727F]/80 font-medium"
              >
                Schedule a Demo
              </button>
              <button
                onClick={() => window.open('https://help.dalti.app', '_blank')}
                className="text-sm text-[#19727F] hover:text-[#19727F]/80 font-medium"
              >
                Visit Help Center
              </button>
            </div>
          </div>

          {/* Reassurance */}
          <div className="mt-6 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-xs text-gray-600 dark:text-gray-400">
              🔒 Your data is safe and secure. You can continue using Dalti with your current plan.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCancel;
