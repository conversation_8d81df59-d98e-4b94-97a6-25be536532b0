import { ApiError } from '../types';
import { <PERSON><PERSON><PERSON><PERSON>og<PERSON>, <PERSON><PERSON>rClassifier, ErrorFormatter } from './error-utils';

/**
 * Subscription-specific error types
 */
export interface SubscriptionError extends ApiError {
  subscriptionErrorType?: SubscriptionErrorType;
  subscriptionContext?: SubscriptionErrorContext;
}

export type SubscriptionErrorType = 
  | 'subscription_not_found'
  | 'subscription_expired'
  | 'subscription_cancelled'
  | 'insufficient_credits'
  | 'payment_failed'
  | 'checkout_expired'
  | 'plan_not_available'
  | 'upgrade_not_allowed'
  | 'downgrade_not_allowed'
  | 'cancellation_failed'
  | 'billing_error'
  | 'webhook_error';

export interface SubscriptionErrorContext {
  subscriptionId?: string;
  planId?: string;
  userId?: string;
  creditsRequired?: number;
  creditsAvailable?: number;
  operation?: string;
}

/**
 * Subscription error classifier
 */
export class SubscriptionErrorClassifier {
  /**
   * Check if error is subscription-related
   */
  static isSubscriptionError(error: any): boolean {
    const subscriptionKeywords = [
      'subscription',
      'billing',
      'payment',
      'credits',
      'plan',
      'checkout'
    ];

    const errorMessage = error?.message?.toLowerCase() || '';
    return subscriptionKeywords.some(keyword => errorMessage.includes(keyword));
  }

  /**
   * Check if error is related to insufficient credits
   */
  static isInsufficientCreditsError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    return message.includes('insufficient credits') || 
           message.includes('not enough credits') ||
           message.includes('credits required') ||
           error?.status === 402; // Payment Required
  }

  /**
   * Check if error is related to expired subscription
   */
  static isSubscriptionExpiredError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    return message.includes('subscription expired') ||
           message.includes('subscription not active') ||
           message.includes('subscription invalid');
  }

  /**
   * Check if error is related to payment failure
   */
  static isPaymentError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    return message.includes('payment failed') ||
           message.includes('payment declined') ||
           message.includes('card declined') ||
           message.includes('billing error');
  }

  /**
   * Check if error is related to checkout session
   */
  static isCheckoutError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    return message.includes('checkout') ||
           message.includes('session expired') ||
           message.includes('invalid session');
  }

  /**
   * Determine subscription error type
   */
  static getSubscriptionErrorType(error: any): SubscriptionErrorType | null {
    const message = error?.message?.toLowerCase() || '';

    if (this.isInsufficientCreditsError(error)) {
      return 'insufficient_credits';
    }

    if (this.isSubscriptionExpiredError(error)) {
      return 'subscription_expired';
    }

    if (this.isPaymentError(error)) {
      return 'payment_failed';
    }

    if (this.isCheckoutError(error)) {
      return 'checkout_expired';
    }

    if (message.includes('subscription not found')) {
      return 'subscription_not_found';
    }

    if (message.includes('subscription cancelled')) {
      return 'subscription_cancelled';
    }

    if (message.includes('plan not available')) {
      return 'plan_not_available';
    }

    if (message.includes('upgrade not allowed')) {
      return 'upgrade_not_allowed';
    }

    if (message.includes('downgrade not allowed')) {
      return 'downgrade_not_allowed';
    }

    if (message.includes('cancellation failed')) {
      return 'cancellation_failed';
    }

    if (message.includes('billing')) {
      return 'billing_error';
    }

    if (message.includes('webhook')) {
      return 'webhook_error';
    }

    return null;
  }
}

/**
 * Subscription error formatter
 */
export class SubscriptionErrorFormatter {
  /**
   * Get user-friendly message for subscription errors
   */
  static getUserFriendlyMessage(error: any): string {
    const errorType = SubscriptionErrorClassifier.getSubscriptionErrorType(error);

    switch (errorType) {
      case 'insufficient_credits':
        return 'You don\'t have enough credits for this action. Please upgrade your plan or purchase additional credits.';
      
      case 'subscription_expired':
        return 'Your subscription has expired. Please renew your subscription to continue using premium features.';
      
      case 'subscription_cancelled':
        return 'Your subscription has been cancelled. Please subscribe to a new plan to access premium features.';
      
      case 'payment_failed':
        return 'Payment failed. Please check your payment method and try again.';
      
      case 'checkout_expired':
        return 'Your checkout session has expired. Please try again.';
      
      case 'plan_not_available':
        return 'The selected plan is not available. Please choose a different plan.';
      
      case 'upgrade_not_allowed':
        return 'Upgrade is not allowed at this time. Please contact support for assistance.';
      
      case 'downgrade_not_allowed':
        return 'Downgrade is not allowed at this time. Please contact support for assistance.';
      
      case 'cancellation_failed':
        return 'Failed to cancel subscription. Please contact support for assistance.';
      
      case 'billing_error':
        return 'A billing error occurred. Please contact support for assistance.';
      
      case 'subscription_not_found':
        return 'Subscription not found. Please contact support if you believe this is an error.';
      
      case 'webhook_error':
        return 'A system error occurred while processing your request. Please try again.';
      
      default:
        // Fall back to general error formatting
        return ErrorFormatter.getUserFriendlyMessage(error);
    }
  }

  /**
   * Get action suggestions for subscription errors
   */
  static getActionSuggestions(error: any): string[] {
    const errorType = SubscriptionErrorClassifier.getSubscriptionErrorType(error);

    switch (errorType) {
      case 'insufficient_credits':
        return [
          'Upgrade to a higher plan',
          'Purchase additional credits',
          'Review your credit usage'
        ];
      
      case 'subscription_expired':
        return [
          'Renew your subscription',
          'Choose a new plan',
          'Contact support for assistance'
        ];
      
      case 'payment_failed':
        return [
          'Update your payment method',
          'Check your card details',
          'Try a different payment method',
          'Contact your bank'
        ];
      
      case 'checkout_expired':
        return [
          'Start a new checkout session',
          'Try again with the same plan'
        ];
      
      default:
        return [
          'Try again',
          'Contact support if the problem persists'
        ];
    }
  }

  /**
   * Format subscription error with context
   */
  static formatWithContext(error: any, context?: SubscriptionErrorContext): SubscriptionError {
    const subscriptionError: SubscriptionError = {
      message: error?.message || 'An unexpected subscription error occurred',
      status: error?.status || 500,
      errors: error?.errors,
      subscriptionErrorType: SubscriptionErrorClassifier.getSubscriptionErrorType(error),
      subscriptionContext: context,
    };

    return subscriptionError;
  }
}

/**
 * Subscription error logger
 */
export class SubscriptionErrorLogger {
  /**
   * Log subscription-specific errors
   */
  static logSubscriptionError(
    error: any, 
    context?: SubscriptionErrorContext,
    operation?: string
  ): void {
    const subscriptionError = SubscriptionErrorFormatter.formatWithContext(error, context);
    
    ErrorLogger.log(subscriptionError, {
      type: 'subscription_error',
      operation,
      subscriptionErrorType: subscriptionError.subscriptionErrorType,
      subscriptionContext: context,
    });
  }

  /**
   * Log credit-related errors
   */
  static logCreditError(
    error: any,
    userId: string,
    creditsRequired?: number,
    creditsAvailable?: number
  ): void {
    this.logSubscriptionError(error, {
      userId,
      creditsRequired,
      creditsAvailable,
      operation: 'credit_check'
    }, 'credit_validation');
  }

  /**
   * Log payment-related errors
   */
  static logPaymentError(
    error: any,
    subscriptionId?: string,
    planId?: string
  ): void {
    this.logSubscriptionError(error, {
      subscriptionId,
      planId,
      operation: 'payment_processing'
    }, 'payment');
  }

  /**
   * Log checkout-related errors
   */
  static logCheckoutError(
    error: any,
    planId: string,
    userId: string
  ): void {
    this.logSubscriptionError(error, {
      planId,
      userId,
      operation: 'checkout_creation'
    }, 'checkout');
  }
}

/**
 * Subscription error handler utility
 */
export class SubscriptionErrorHandler {
  /**
   * Handle subscription errors with appropriate user feedback
   */
  static handleError(
    error: any,
    context?: SubscriptionErrorContext,
    onRetry?: () => void
  ): {
    message: string;
    actions: string[];
    canRetry: boolean;
    requiresUpgrade: boolean;
  } {
    const errorType = SubscriptionErrorClassifier.getSubscriptionErrorType(error);
    const message = SubscriptionErrorFormatter.getUserFriendlyMessage(error);
    const actions = SubscriptionErrorFormatter.getActionSuggestions(error);

    // Log the error
    SubscriptionErrorLogger.logSubscriptionError(error, context);

    // Determine if retry is possible
    const canRetry = !ErrorClassifier.isAuthError(error) && 
                     !SubscriptionErrorClassifier.isInsufficientCreditsError(error) &&
                     !SubscriptionErrorClassifier.isSubscriptionExpiredError(error);

    // Determine if upgrade is required
    const requiresUpgrade = SubscriptionErrorClassifier.isInsufficientCreditsError(error) ||
                           SubscriptionErrorClassifier.isSubscriptionExpiredError(error);

    return {
      message,
      actions,
      canRetry,
      requiresUpgrade,
    };
  }

  /**
   * Create subscription error notification
   */
  static createErrorNotification(error: any, context?: SubscriptionErrorContext) {
    const errorType = SubscriptionErrorClassifier.getSubscriptionErrorType(error);
    const message = SubscriptionErrorFormatter.getUserFriendlyMessage(error);

    return {
      type: 'error' as const,
      title: this.getErrorTitle(errorType),
      message,
      timestamp: new Date().toISOString(),
      read: false,
      actionUrl: this.getActionUrl(errorType),
      actionText: this.getActionText(errorType),
    };
  }

  private static getErrorTitle(errorType: SubscriptionErrorType | null): string {
    switch (errorType) {
      case 'insufficient_credits':
        return 'Insufficient Credits';
      case 'subscription_expired':
        return 'Subscription Expired';
      case 'payment_failed':
        return 'Payment Failed';
      case 'checkout_expired':
        return 'Checkout Expired';
      default:
        return 'Subscription Error';
    }
  }

  private static getActionUrl(errorType: SubscriptionErrorType | null): string | undefined {
    switch (errorType) {
      case 'insufficient_credits':
      case 'subscription_expired':
        return '/subscription/plans';
      case 'payment_failed':
        return '/subscription/billing';
      default:
        return undefined;
    }
  }

  private static getActionText(errorType: SubscriptionErrorType | null): string | undefined {
    switch (errorType) {
      case 'insufficient_credits':
        return 'Upgrade Plan';
      case 'subscription_expired':
        return 'Renew Subscription';
      case 'payment_failed':
        return 'Update Payment';
      default:
        return undefined;
    }
  }
}
