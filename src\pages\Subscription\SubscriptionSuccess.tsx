import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useSubscriptionStatus } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';

const SubscriptionSuccess: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { provider } = useAuth();
  const { refetch } = useSubscriptionStatus();

  const isUpgrade = searchParams.get('upgrade') === 'true';
  const isDowngrade = searchParams.get('downgrade') === 'true';

  useEffect(() => {
    // Refetch subscription status to get updated data
    refetch();
  }, [refetch]);

  const getTitle = () => {
    if (isUpgrade) return 'Subscription Upgraded Successfully!';
    if (isDowngrade) return 'Subscription Changed Successfully!';
    return 'Welcome to Dalti!';
  };

  const getMessage = () => {
    if (isUpgrade) {
      return 'Your subscription has been upgraded and you now have access to enhanced features and additional credits.';
    }
    if (isDowngrade) {
      return 'Your subscription has been updated. The changes will take effect at your next billing cycle.';
    }
    return 'Thank you for subscribing! Your account has been activated and you now have access to all premium features.';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          {/* Success Icon */}
          <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/20 mb-6">
            <svg className="h-12 w-12 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {getTitle()}
          </h1>

          {/* Message */}
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            {getMessage()}
          </p>

          {/* Business Name */}
          {provider?.title && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 border border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400">Subscription for</p>
              <p className="font-semibold text-gray-900 dark:text-white">{provider.title}</p>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800/50 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              What's Next?
            </h3>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1 text-left">
              <li>• Access your subscription dashboard</li>
              <li>• Monitor your credit usage</li>
              <li>• Explore premium features</li>
              <li>• Set up your services and locations</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={() => navigate('/subscription')}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#19727F] hover:bg-[#19727F]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              View Subscription Dashboard
            </button>
            
            <button
              onClick={() => navigate('/')}
              className="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#19727F] transition-colors"
            >
              Go to Dashboard
            </button>
          </div>

          {/* Support */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              Need help getting started?
            </p>
            <button
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="text-sm text-[#19727F] hover:text-[#19727F]/80 font-medium"
            >
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionSuccess;
