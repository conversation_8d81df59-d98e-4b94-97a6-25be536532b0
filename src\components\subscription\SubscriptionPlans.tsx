import React from 'react';
import clsx from 'clsx';
import { useSubscriptionPlans, useCreateCheckoutWithContext } from '../../hooks/useSubscription';
import { SubscriptionPlansProps } from '../../types';
import { ErrorDisplay } from '../error/ErrorDisplay';
import { Button } from '../ui/button/Button';

const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  currentPlanId,
  onPlanSelect,
  showCurrentPlan = true,
  highlightUpgrades = true,
}) => {
  const { data: plansResponse, isLoading, error, refetch } = useSubscriptionPlans();
  const createCheckout = useCreateCheckoutWithContext();

  const handlePlanSelect = async (planId: string) => {
    if (onPlanSelect) {
      onPlanSelect(planId);
      return;
    }

    // Default behavior: create checkout session
    try {
      await createCheckout.mutateAsync({ planId });
    } catch (error) {
      // Error is handled by the hook
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4 dark:bg-gray-700 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto dark:bg-gray-700 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border border-gray-200 rounded-2xl p-6 dark:border-gray-700 animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/2 mb-4 dark:bg-gray-700"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-4 dark:bg-gray-700"></div>
              <div className="space-y-2 mb-6">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="h-4 bg-gray-200 rounded dark:bg-gray-700"></div>
                ))}
              </div>
              <div className="h-10 bg-gray-200 rounded dark:bg-gray-700"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay 
        error={error} 
        title="Failed to load subscription plans"
        showRetry={true}
        onRetry={refetch}
        variant="card"
      />
    );
  }

  if (!plansResponse?.success || !plansResponse.data?.subscriptions) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">No subscription plans available</p>
      </div>
    );
  }

  const plans = plansResponse.data.subscriptions;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Choose Your Subscription Plan
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Select the perfect plan for your business needs. Upgrade or downgrade anytime.
        </p>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-7xl mx-auto">
        {plans.map((plan) => {
          const isCurrentPlan = currentPlanId === plan.id;
          const isFree = plan.price === 0;
          const isPopular = plan.isPopular;

          return (
            <div
              key={plan.id}
              className={clsx(
                "relative rounded-2xl border p-6 transition-all duration-200",
                isPopular 
                  ? "border-[#19727F] shadow-lg scale-105 bg-white dark:bg-gray-800" 
                  : "border-gray-200 hover:border-gray-300 bg-white dark:border-gray-700 dark:bg-gray-800 hover:shadow-md",
                isCurrentPlan && showCurrentPlan && "ring-2 ring-green-500 ring-opacity-50"
              )}
            >
              {/* Popular Badge */}
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="inline-flex items-center px-4 py-1 rounded-full text-xs font-medium bg-[#19727F] text-white">
                    Most Popular
                  </span>
                </div>
              )}

              {/* Current Plan Badge */}
              {isCurrentPlan && showCurrentPlan && (
                <div className="absolute -top-3 right-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500 text-white">
                    Current Plan
                  </span>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {plan.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  {plan.description}
                </p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900 dark:text-white">
                    {plan.priceFormatted}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400 ml-1">
                    /{plan.interval}
                  </span>
                </div>
              </div>

              {/* Credits Info */}
              <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="text-center">
                  <span className="text-2xl font-bold text-[#19727F] dark:text-[#19727F]">
                    {plan.creditsIncluded.toLocaleString()}
                  </span>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    credits included
                  </p>
                </div>
              </div>

              {/* Features List */}
              <div className="mb-8">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <svg 
                        className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path 
                          strokeLinecap="round" 
                          strokeLinejoin="round" 
                          strokeWidth={2} 
                          d="M5 13l4 4L19 7" 
                        />
                      </svg>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action Button */}
              <div className="mt-auto">
                {isCurrentPlan && showCurrentPlan ? (
                  <Button
                    variant="outline"
                    className="w-full"
                    disabled
                  >
                    Current Plan
                  </Button>
                ) : (
                  <Button
                    variant={isPopular ? "primary" : "outline"}
                    className={clsx(
                      "w-full",
                      isPopular && "bg-[#19727F] hover:bg-[#19727F]/90 text-white",
                      !isPopular && "border-[#19727F] text-[#19727F] hover:bg-[#19727F] hover:text-white"
                    )}
                    onClick={() => handlePlanSelect(plan.id)}
                    disabled={createCheckout.isPending}
                  >
                    {createCheckout.isPending ? (
                      <div className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </div>
                    ) : (
                      <>
                        {isFree ? 'Get Started' : `Upgrade to ${plan.name}`}
                      </>
                    )}
                  </Button>
                )}
              </div>

              {/* Upgrade Indicator */}
              {highlightUpgrades && currentPlanId && !isCurrentPlan && !isFree && (
                <div className="mt-3 text-center">
                  <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                    ⬆️ Upgrade Available
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="text-center text-sm text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
        <p className="mb-2">
          All plans include secure payment processing and 24/7 customer support.
        </p>
        <p>
          You can upgrade, downgrade, or cancel your subscription at any time.
        </p>
      </div>
    </div>
  );
};

export default SubscriptionPlans;
