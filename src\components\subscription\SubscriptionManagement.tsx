import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import { Modal } from '../ui/modal';
import { useSubscriptionStatus, useCancelSubscription } from '../../hooks/useSubscription';
import { useAuth } from '../../context/AuthContext';
import { ErrorDisplay } from '../error/ErrorDisplay';
import { Button } from '../ui/button/Button';
import SubscriptionPlans from './SubscriptionPlans';
import CreditsUsage from './CreditsUsage';

interface SubscriptionManagementProps {
  isOpen: boolean;
  onClose: () => void;
  initialTab?: 'overview' | 'plans' | 'credits' | 'billing';
}

const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({
  isOpen,
  onClose,
  initialTab = 'overview',
}) => {
  const navigate = useNavigate();
  const { provider } = useAuth();
  const [activeTab, setActiveTab] = useState(initialTab);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');

  const { data: subscriptionResponse, isLoading, error, refetch } = useSubscriptionStatus();
  const cancelSubscription = useCancelSubscription();

  const handleCancelSubscription = async () => {
    if (!subscriptionResponse?.data?.subscription?.id) return;

    try {
      await cancelSubscription.mutateAsync({
        subscriptionId: subscriptionResponse.data.subscription.id,
        reason: cancelReason,
        feedback: cancelFeedback,
      });
      setShowCancelConfirm(false);
      onClose();
      refetch();
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'plans', label: 'Plans', icon: '📋' },
    { id: 'credits', label: 'Credits', icon: '💳' },
    { id: 'billing', label: 'Billing', icon: '💰' },
  ];

  const renderTabContent = () => {
    if (isLoading) {
      return (
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4 dark:bg-gray-700"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
          <div className="h-32 bg-gray-200 rounded dark:bg-gray-700"></div>
        </div>
      );
    }

    if (error) {
      return (
        <ErrorDisplay 
          error={error} 
          title="Failed to load subscription information"
          showRetry={true}
          onRetry={refetch}
          variant="card"
        />
      );
    }

    const subscriptionData = subscriptionResponse?.data;
    const subscription = subscriptionData?.subscription;

    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Current Subscription */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Current Subscription
              </h3>
              {subscriptionData?.hasActiveSubscription && subscription ? (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Plan</p>
                      <p className="font-semibold text-gray-900 dark:text-white">{subscription.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 capitalize">
                        {subscription.status}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Price</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {subscription.priceFormatted}/{subscription.interval}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Next Billing</p>
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {new Date(subscription.endDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800/50 rounded-lg p-4">
                  <p className="text-yellow-800 dark:text-yellow-200">
                    No active subscription. Upgrade to access premium features.
                  </p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <button
                  onClick={() => setActiveTab('plans')}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                >
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">📋</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">View Plans</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Compare and upgrade</p>
                    </div>
                  </div>
                </button>
                <button
                  onClick={() => navigate('/subscription/billing')}
                  className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                >
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">💰</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Billing History</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">View past invoices</p>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            {/* Cancel Subscription */}
            {subscriptionData?.hasActiveSubscription && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Subscription Management
                </h3>
                <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800/50 rounded-lg p-4">
                  <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
                    Cancel Subscription
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-300 mb-4">
                    Cancelling will end your subscription at the end of the current billing period.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCancelConfirm(true)}
                    className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/20"
                  >
                    Cancel Subscription
                  </Button>
                </div>
              </div>
            )}
          </div>
        );

      case 'plans':
        return (
          <div>
            <SubscriptionPlans 
              currentPlanId={subscription?.id}
              showCurrentPlan={true}
              highlightUpgrades={true}
            />
          </div>
        );

      case 'credits':
        return (
          <div>
            <CreditsUsage 
              showHistory={true}
              showAnalytics={true}
              warningThreshold={20}
            />
          </div>
        );

      case 'billing':
        return (
          <div className="text-center py-8">
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Billing history is available on the dedicated billing page.
            </p>
            <Button
              onClick={() => {
                onClose();
                navigate('/subscription/billing');
              }}
            >
              View Billing History
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        className="max-w-4xl p-0"
        showCloseButton={false}
      >
        <div className="flex flex-col h-full max-h-[80vh]">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Subscription Management
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Manage your {provider?.title || 'business'} subscription and billing
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-[#19727F] text-[#19727F]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {renderTabContent()}
          </div>
        </div>
      </Modal>

      {/* Cancel Confirmation Modal */}
      <Modal
        isOpen={showCancelConfirm}
        onClose={() => setShowCancelConfirm(false)}
        className="max-w-md p-0"
        showCloseButton={false}
      >
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cancel Subscription
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            We're sorry to see you go. Please let us know why you're cancelling so we can improve our service.
          </p>
          
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason for cancelling *
              </label>
              <select
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300"
                required
              >
                <option value="">Select a reason</option>
                <option value="too_expensive">Too expensive</option>
                <option value="not_using">Not using enough</option>
                <option value="missing_features">Missing features</option>
                <option value="technical_issues">Technical issues</option>
                <option value="switching_provider">Switching to another provider</option>
                <option value="business_closure">Business closure</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Additional feedback (optional)
              </label>
              <textarea
                value={cancelFeedback}
                onChange={(e) => setCancelFeedback(e.target.value)}
                rows={3}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300"
                placeholder="Help us improve by sharing your experience..."
              />
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowCancelConfirm(false)}
              className="flex-1"
            >
              Keep Subscription
            </Button>
            <Button
              onClick={handleCancelSubscription}
              disabled={!cancelReason || cancelSubscription.isPending}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              {cancelSubscription.isPending ? 'Cancelling...' : 'Cancel Subscription'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SubscriptionManagement;
