/**
 * Subscription and billing related types
 */

// Subscription Plan Types
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  priceFormatted: string;
  interval: 'monthly' | 'yearly' | 'one-time';
  creditsIncluded: number;
  features: string[];
  isActive: boolean;
  isPopular: boolean;
}

// User Subscription Types
export interface UserSubscription {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired' | 'pending';
  startDate: string;
  endDate: string;
  daysRemaining: number;
  creditsAllocated: number;
  price: number;
  priceFormatted: string;
  interval: 'monthly' | 'yearly' | 'one-time';
}

// Credits Data Types
export interface CreditsData {
  current: number;
  allocated: number;
  used: number;
  percentage: number;
}

export interface CreditHistoryEntry {
  date: string;
  change: number;
  reason: string;
  balance: number;
}

export interface UserCreditsResponse {
  userId: string;
  credits: number;
  lastUpdated: string;
  creditHistory: CreditHistoryEntry[];
}

// Subscription Status Types
export interface SubscriptionStatusData {
  hasActiveSubscription: boolean;
  subscription: UserSubscription | null;
  credits: CreditsData;
}

// Checkout Types
export interface CheckoutRequest {
  planIdentifier: string;
  returnUrl: string;
  cancelUrl: string;
  customerInfo: {
    email: string;
    name: string;
  };
}

export interface CheckoutSession {
  sessionId: string;
  sessionUrl: string;
  expiresAt: string;
}

// Billing History Types
export interface BillingTransaction {
  id: string;
  subscriptionName: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired' | 'pending';
  startDate: string;
  endDate: string;
  price: number;
  priceFormatted: string;
  creditsAllocated: number;
}

export interface BillingHistoryResponse {
  subscriptions: BillingTransaction[];
  total: number;
}

// Subscription Cancellation Types
export interface SubscriptionCancellationRequest {
  subscriptionId: string;
  reason: string;
  feedback?: string;
}

// Available Plans Response Types
export interface SubscriptionPlansResponse {
  subscriptions: SubscriptionPlan[];
  total: number;
}

// Webhook Types (Optional)
export interface WebhookConfig {
  url: string;
  secret: string;
  events: WebhookEventType[];
}

export type WebhookEventType = 
  | 'subscription.created'
  | 'subscription.updated'
  | 'subscription.cancelled'
  | 'payment.succeeded'
  | 'payment.failed'
  | 'credits.updated';

export interface WebhookEvent {
  eventType: WebhookEventType;
  data: any;
  timestamp: string;
}

// API Response Types
export interface SubscriptionStatusResponse {
  success: boolean;
  data: SubscriptionStatusData;
}

export interface CheckoutResponse {
  success: boolean;
  data: CheckoutSession;
}

export interface SubscriptionPlansApiResponse {
  success: boolean;
  data: SubscriptionPlansResponse;
}

export interface UserCreditsApiResponse {
  success: boolean;
  data: UserCreditsResponse;
}

export interface BillingHistoryApiResponse {
  success: boolean;
  data: BillingHistoryResponse;
}

// Subscription Management Types
export interface SubscriptionManagementState {
  currentSubscription: UserSubscription | null;
  availablePlans: SubscriptionPlan[];
  credits: CreditsData | null;
  billingHistory: BillingTransaction[];
  isLoading: boolean;
  error: string | null;
}

// Subscription Form Types
export interface SubscriptionUpgradeRequest {
  newPlanId: string;
  prorated?: boolean;
}

export interface SubscriptionDowngradeRequest {
  newPlanId: string;
  effectiveDate?: string; // When the downgrade should take effect
}

// Notification Types for Subscriptions
export interface SubscriptionNotification {
  type: 'low_credits' | 'subscription_expiring' | 'payment_failed' | 'subscription_renewed';
  title: string;
  message: string;
  threshold?: number; // For credit warnings
  daysUntilExpiry?: number; // For expiration warnings
  actionRequired?: boolean;
  actionUrl?: string;
}

// Credit Usage Analytics
export interface CreditUsageAnalytics {
  dailyUsage: TimeSeriesDataPoint[];
  weeklyUsage: TimeSeriesDataPoint[];
  monthlyUsage: TimeSeriesDataPoint[];
  topUsageCategories: ChartDataPoint[];
  averageDailyUsage: number;
  projectedMonthlyUsage: number;
}

// Subscription Analytics
export interface SubscriptionAnalytics {
  subscriptionDuration: number; // Days since subscription started
  totalCreditsUsed: number;
  averageCreditsPerDay: number;
  mostActiveDay: string;
  leastActiveDay: string;
  usageEfficiency: number; // Percentage of allocated credits used
}

// Import common types for analytics
import { TimeSeriesDataPoint, ChartDataPoint } from './index';

// Subscription Hook State Types
export interface UseSubscriptionStatusResult {
  subscriptionStatus: SubscriptionStatusData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseSubscriptionPlansResult {
  plans: SubscriptionPlan[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseCreditsResult {
  credits: UserCreditsResponse | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Subscription Component Props Types
export interface SubscriptionStatusCardProps {
  className?: string;
  showActions?: boolean;
  compact?: boolean;
}

export interface SubscriptionPlansProps {
  currentPlanId?: string;
  onPlanSelect?: (planId: string) => void;
  showCurrentPlan?: boolean;
  highlightUpgrades?: boolean;
}

export interface CreditsUsageProps {
  showHistory?: boolean;
  showAnalytics?: boolean;
  warningThreshold?: number;
  className?: string;
}

export interface BillingHistoryProps {
  pageSize?: number;
  showFilters?: boolean;
  allowExport?: boolean;
  className?: string;
}
